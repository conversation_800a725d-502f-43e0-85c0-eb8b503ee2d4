// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'ru.cleverpumpkin.proguard-dictionaries-generator' version '1.0.8' apply false
    id 'com.android.application' version '8.5.0' apply false
    id 'com.android.library' version '8.5.0' apply false
    id 'org.jetbrains.kotlin.android' version '2.0.10' apply false
}

tasks.register('clean', Delete) {
    delete rootProject.layout.buildDirectory
}