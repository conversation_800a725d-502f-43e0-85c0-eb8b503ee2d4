


# TVBox源使用教程

昨天更新之后，很多人问如何使用，今天写一篇补充教程。

## 地址
https://androidcatvodspider.netlify.app/json/index.json

## 如何操作

1. 清除源缓存，更新到最新的源
2. 站源切换到`Cookie设置`
3. 选择分类`UC`,然后点击设置Token，弹出二维码后，用手机端扫码即可。
4. 如果以前使用过UC，那么就可以直接享受UC原画高清视频。如果是本源的新用户，需要找一个UC资源观看，他会自动弹出窗口，然后选择扫二维码扫码即可观看。

## 存在问题
1. 有的新用户一直提示扫码，可能是TVBOX的文件权限没开，无法在本地生成文件，打开文件权限就行。
2. 有的旧用户设置了Cookie还是无法观看，可以到文件管理里面找到TV文件夹，删除里面的`.uc` 和`.uctoken`文件（可能被隐藏，设置里打开显示隐藏文件，或者将文件夹删除）,再重新按照操作走一遍。