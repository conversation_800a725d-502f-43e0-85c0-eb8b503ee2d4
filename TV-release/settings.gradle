pluginManagement {
    repositories {
        gradlePluginPortal()
        mavenCentral()
        google()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        mavenCentral()
        google()
        flatDir { dirs "$rootDir/app/libs" }
        maven { url "https://jitpack.io" }
        maven {
            url "http://4thline.org/m2"
            allowInsecureProtocol = true
        }
    }
}
include ':app'
include ':catvod'
include ':chaquo'
include ':quickjs'
rootProject.name = "TV"
