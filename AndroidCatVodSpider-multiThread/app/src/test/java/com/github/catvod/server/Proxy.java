package com.github.catvod.server;


import java.io.InputStream;
import java.util.Map;

import fi.iki.elonen.NanoHTTPD;

public class Proxy implements Process {

    @Override
    public boolean isRequest(NanoHTTPD.IHTTPSession session, String path) {
        return "/proxy".equals(path);
    }

    @Override
    public NanoHTTPD.Response doResponse(NanoHTTPD.IHTTPSession session, String path, Map<String, String> files) {
        try {

            Map<String, String> params = session.getParms();
            params.putAll(session.getHeaders());
            Object[] rs = com.github.catvod.spider.Proxy.proxy(params);
            if (rs[0] instanceof NanoHTTPD.Response) return (NanoHTTPD.Response) rs[0];
            NanoHTTPD.Response response = NanoHTTPD.newChunkedResponse(NanoHTTPD.Response.Status.lookup((Integer) rs[0]), (String) rs[1], (InputStream) rs[2]);
            if (rs.length > 3 && rs[3] != null)
                for (Map.Entry<String, String> entry : ((Map<String, String>) rs[3]).entrySet())
                    response.addHeader(entry.getKey(), entry.getValue());
            return response;
        } catch (Exception e) {
            return Nano.error(e.getMessage());
        }
    }

}
