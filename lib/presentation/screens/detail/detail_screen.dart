import 'package:flutter/material.dart';

import 'package:provider/provider.dart';

import '../../../data/models/video_info.dart';
import '../../../presentation/providers/data_source_state.dart';
import '../../pages/quark_settings_page.dart';
import '../../pages/player_page.dart';
import '../../../services/quark/quark_channel.dart';
import '../../../services/quark/quark_cookie_service.dart';
import '../../../services/spider/wogg_detail_fetch_service.dart';
import '../../../services/spider/wogg_detail_parser.dart';
import 'package:free_tv_player/presentation/widgets/cloud_drive_login_webview.dart';
import 'package:free_tv_player/services/quark/quark_login_delegate.dart';

/// 视频详情屏幕（读取首页传入的 VideoInfo，展示基础信息 + 播放源(夸克优先) + 选集）
class DetailScreen extends StatefulWidget {
  const DetailScreen({super.key});

  @override
  State<DetailScreen> createState() => _DetailScreenState();
}

class _DetailScreenState extends State<DetailScreen> {
  VideoInfo? _videoArg; // 首页传入的视频

  // “播放源”相关状态
  List<CloudDriveSource> _sources = []; // 各网盘分组（夸克优先）
  int _selectedSourceIndex = 0; // 当前选中的播放源索引（默认优先夸克）

  // 夸克解析状态（当选择的播放源为夸克时生效）
  final List<String> _quarkLinks = []; // 夸克分享链接列表（自动解析）
  final Map<String, String> _quarkPwds = {}; // 链接 -> 提取码（可选）
  Map<String, dynamic>? _quarkVod; // detail 返回的 JSON（含 seriesFlags/seriesMap）
  String? _selectedFlag; // 当前选中的线路
  bool _loadingVod = false; // 是否在加载选集
  bool _isLoginSheetShowing = false; // 是否正在展示扫码登录弹框（防止重复弹出）

  // 输入框控制器（用于粘贴分享链接；需求2：不再展示链接，保留此作兜底手段，可隐藏）
  final TextEditingController _linkCtrl = TextEditingController();
  final TextEditingController _pwdCtrl = TextEditingController();

  @override
  void dispose() {
    _linkCtrl.dispose();
    _pwdCtrl.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 从路由参数中提取 VideoInfo（仅执行一次，避免重复加载与重复弹框）
    if (_videoArg != null) return;
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args is VideoInfo) {
      _videoArg = args;
      _autoFetchSources(args);
    }
  }

  /// 自动拉取 wogg 详情并组装“播放源”，默认选中夸克并在有 Cookie 时自动解析
  Future<void> _autoFetchSources(VideoInfo v) async {
    try {
      debugPrint('[Detail] start _autoFetchSources id=${v.id} name=${v.name}');
      final fetcher = WoggDetailFetchService();
      final result = await fetcher.fetchAndParseByVideo(v);
      if (!mounted || result == null) {
        debugPrint('[Detail] fetchAndParse null');
        return;
      }
      // 组装源并确定默认索引：夸克优先（没有则第一个）
      final sources = result.sources;
      debugPrint(
        '[Detail] sources total=${sources.length} -> ${sources.map((e) => e.name).join(', ')}',
      );
      if (sources.isEmpty) return;
      int defaultIndex = 0;
      for (int i = 0; i < sources.length; i++) {
        if (sources[i].type.contains('夸克')) {
          defaultIndex = i;
          break;
        }
      }
      setState(() {
        _sources = sources;
        _selectedSourceIndex = defaultIndex;
      });

      // 若默认源为夸克：收集链接并根据 Cookie 自动解析
      final sel = sources[defaultIndex];
      if (sel.type.contains('夸克')) {
        _quarkLinks.clear();
        _quarkPwds.clear();
        for (final q in sel.links) {
          _quarkLinks.add(q.url);
          if (q.pwd != null && q.pwd!.isNotEmpty) _quarkPwds[q.url] = q.pwd!;
        }
        // 中文调试：打印链接明细，避免隐去关键信息导致排查困难
        for (int i = 0; i < _quarkLinks.length; i++) {
          debugPrint(
            '[Detail] quark link[$i] = ${_quarkLinks[i]} pwd=${_quarkPwds[_quarkLinks[i]] ?? ''}',
          );
        }
        debugPrint(
          '[Detail] quark links=${_quarkLinks.length}, pwds=${_quarkPwds.length}',
        );
        final hasCookie = await QuarkCookieService.hasCookie();
        if (!mounted) return;
        if (hasCookie) {
          // 自动开始解析
          debugPrint('[Detail] has cookie, auto load quark vod');
          _onLoadQuarkVod();
        } else {
          // 无 Cookie：直接提示并拉起扫码登录 WebView
          debugPrint('[Detail] no cookie, open login webview');
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('当前播放源需要先登录夸克网盘')));
          if (!_isLoginSheetShowing) {
            _isLoginSheetShowing = true;
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              final cookie = await showModalBottomSheet<String>(
                context: context,
                isScrollControlled: true,
                useSafeArea: true,
                backgroundColor: Colors.transparent,
                builder: (_) {
                  final height = MediaQuery.of(context).size.height * 0.85;
                  return _buildLoginSheet(height: height);
                },
              );
              _isLoginSheetShowing = false;
              if (!mounted) return;
              if (cookie != null && cookie.isNotEmpty) {
                debugPrint('[Detail] login success, reload quark vod');
                _onLoadQuarkVod();
              }
            });
          }
        }
      }
    } catch (e) {
      debugPrint('[Detail] 自动解析播放源失败：$e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final dataSourceState = context.watch<DataSourceState>();
    final v = _videoArg;

    return Scaffold(
      appBar: AppBar(
        title: Text(v?.name ?? '视频详情'),
        actions: [
          IconButton(
            tooltip: '夸克设置',
            icon: const Icon(Icons.cookie),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => const QuarkSettingsPage()),
              );
            },
          ),
        ],
      ),
      body: v == null
          ? const Center(child: Text('未获取到视频信息'))
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 1) 基础信息
                  _buildBasicInfo(v),

                  const SizedBox(height: 20),
                  // 2) 播放源（展示所有云盘，夸克优先；同名去重并可加序号）
                  Text('播放源', style: Theme.of(context).textTheme.titleMedium),
                  const SizedBox(height: 8),
                  _buildSourceChips(),

                  const SizedBox(height: 12),
                  // 3) 夸克选集区（隐藏原始链接，仅展示“加载选集/线路/选集”）
                  if (_isQuarkSelected()) _buildQuarkSection(),

                  const SizedBox(height: 12),
                  if (dataSourceState.currentDataSource == null)
                    const Text(
                      '未选中数据源，部分功能可能不可用',
                      style: TextStyle(color: Colors.orange),
                    ),
                ],
              ),
            ),
    );
  }

  /// 基础信息区（封面 + 文本）
  Widget _buildBasicInfo(VideoInfo v) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: v.pic != null && v.pic!.isNotEmpty
              ? Image.network(
                  v.pic!,
                  width: 150,
                  height: 210,
                  fit: BoxFit.cover,
                )
              : Container(
                  width: 150,
                  height: 210,
                  color: Colors.grey[300],
                  child: const Icon(Icons.movie, size: 48, color: Colors.grey),
                ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(v.name, style: Theme.of(context).textTheme.titleLarge),
              const SizedBox(height: 8),
              if (v.note != null && v.note!.isNotEmpty)
                Text(v.note!, style: Theme.of(context).textTheme.bodyMedium),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  if (v.type != null) _chip('类型', v.type!),
                  if (v.year != null) _chip('年份', '${v.year}'),
                  if (v.area != null) _chip('地区', v.area!),
                ],
              ),
              const SizedBox(height: 8),
              if (v.content != null && v.content!.isNotEmpty)
                Text(v.content!, maxLines: 6, overflow: TextOverflow.ellipsis),
            ],
          ),
        ),
      ],
    );
  }

  /// 是否当前选中夸克源（中文注释）
  bool _isQuarkSelected() {
    if (_sources.isEmpty) return false;
    final s = _sources[_selectedSourceIndex];
    return s.type.contains('夸克');
  }

  /// 切换到夸克源后自动尝试加载选集（无论是否已登录；未登录会自动引导扫码）
  void _autoLoadIfQuarkSelected() async {
    if (!_isQuarkSelected()) return;
    // 若尚未加载过选集，则自动触发加载
    if (_quarkVod == null) {
      _onLoadQuarkVod();
    }
  }

  /// 渲染播放源 Chips（按 _sources 渲染，支持切换源）
  Widget _buildSourceChips() {
    if (_sources.isEmpty) return const Text('暂无播放源');
    return Wrap(
      spacing: 8,
      children: [
        for (int i = 0; i < _sources.length; i++)
          ChoiceChip(
            label: Text(_sources[i].name),
            selected: _selectedSourceIndex == i,
            onSelected: (v) {
              if (!v) return;
              setState(() {
                _selectedSourceIndex = i;
                _quarkVod = null; // 切换源时清空已加载的选集
                _selectedFlag = null;
                // 若切换到夸克源，同步链接集合（隐藏 UI，不展示链接文本）
                if (_isQuarkSelected()) {
                  _quarkLinks
                    ..clear()
                    ..addAll(_sources[i].links.map((e) => e.url));
                  _quarkPwds
                    ..clear()
                    ..addEntries(
                      _sources[i].links
                          .where((e) => (e.pwd ?? '').isNotEmpty)
                          .map((e) => MapEntry(e.url, e.pwd!)),
                    );
                }
              });
              // 中文注释：切换到夸克源后自动加载选集（无需按钮）
              _autoLoadIfQuarkSelected();
            },
          ),
      ],
    );
  }

  /// 夸克选集区（需求变更：移除“加载按钮”栏，切换到夸克源即自动加载）
  Widget _buildQuarkSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('夸克选集', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),

        // 提示文案（无链接时）
        if (_quarkLinks.isEmpty)
          const Padding(
            padding: EdgeInsets.only(bottom: 8),
            child: Text('未检测到夸克分享链接，若资源站更新可稍后重试。'),
          ),

        // 已加载：展示“清晰度（原线路） + 选集”
        if (_quarkVod != null) ...[
          const Divider(),
          Text('清晰度', style: Theme.of(context).textTheme.titleSmall),
          const SizedBox(height: 8),
          _buildFlagChips(),
          const SizedBox(height: 12),
          Text('选集', style: Theme.of(context).textTheme.titleSmall),
          const SizedBox(height: 8),
          _buildEpisodes(),
        ] else ...[
          // 若未加载，尝试引导（但按钮已移除）
          Row(
            children: const [
              Icon(Icons.info_outline, size: 16, color: Colors.grey),
              SizedBox(width: 6),
              Expanded(child: Text('正在等待加载选集…若未自动开始，请检查是否已登录夸克。')),
            ],
          ),
        ],
      ],
    );
  }

  /// 渲染清晰度（原“线路”） chips
  Widget _buildFlagChips() {
    final flags =
        (_quarkVod?['seriesFlags'] as List?)?.cast<String>() ?? const [];
    if (flags.isEmpty) return const Text('暂无清晰度');
    final sel = _selectedFlag ?? flags.first;
    return Wrap(
      spacing: 8,
      children: [
        for (final f in flags)
          ChoiceChip(
            label: Text(f),
            selected: f == sel,
            onSelected: (_) => setState(() => _selectedFlag = f),
          ),
      ],
    );
  }

  /// 渲染选集按钮（当仅有“分享入口”或空时，提示而不渲染按钮）
  Widget _buildEpisodes() {
    final flags =
        (_quarkVod?['seriesFlags'] as List?)?.cast<String>() ?? const [];
    if (flags.isEmpty) return const SizedBox.shrink();
    final sel = _selectedFlag ?? flags.first;
    debugPrint('[Detail] episodes flag=$sel');

    final seriesMap =
        (_quarkVod?['seriesMap'] as Map?)?.cast<String, dynamic>() ?? {};
    final eps = (seriesMap[sel] as List?)?.cast<Map>() ?? const [];
    debugPrint('[Detail] episodes count=${eps.length}');

    // 仅有 0 或 1 条，多数情况是“分享入口”，提示文案，避免误导
    if (eps.length <= 1) {
      return const Text('未解析到分集或仅有分享入口，稍后可重试或更换线路');
    }

    String _epLabel(Map m, int index) {
      // 名称为空或命中“分享N”时，回退为“第N集”
      final name = (m['name'] as String?)?.trim() ?? '';
      // 正则：以“分享数字”整串匹配，如“分享1”、“分享12”
      final sharePattern = RegExp(r'^分享\d+ ?$');
      if (name.isEmpty || sharePattern.hasMatch(name) || name == '分享') {
        // 调试：打印原始分集名称，帮助判断是否命中“分享N”
        debugPrint('[Detail] ep label raw="$name" index=$index');

        return '第${index + 1}集';
      }
      return name;
    }

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        for (int i = 0; i < eps.length; i++)
          ElevatedButton(
            onPressed: () {
              final e = eps[i].cast<String, dynamic>();
              final id = e['url'] as String? ?? '';
              if (id.isEmpty) return;
              _playViaQuark(sel, id);
            },
            child: Text(_epLabel(eps[i].cast<String, dynamic>(), i)),
          ),
      ],
    );
  }

  /// 加载夸克选集：校验 Cookie -> 调用 detail（无 Cookie 直接打开扫码登录 WebView）
  /// 小改动（方案1）：若当前夸克分组返回的分集数 <= 1，则自动尝试同页的其他夸克分组，
  /// 选择“分集数最多”的结果作为最终展示，尽量规避某个分组失效/仅为目录入口的情况。
  Future<void> _onLoadQuarkVod() async {
    if (_quarkLinks.isEmpty) {
      debugPrint('[Detail] _onLoadQuarkVod no links');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('未检测到夸克分享链接')));
      return;
    }

    // 1) 检查 Cookie：若无则直接提示并打开扫码登录
    final hasCookie = await QuarkCookieService.hasCookie();
    if (!hasCookie) {
      if (!mounted) return;
      debugPrint('[Detail] _onLoadQuarkVod no cookie -> open login');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('当前播放源需要先登录夸克网盘')));
      final cookie = await showModalBottomSheet<String>(
        context: context,
        isScrollControlled: true,
        useSafeArea: true,
        backgroundColor: Colors.transparent,
        builder: (_) {
          final height = MediaQuery.of(context).size.height * 0.85;
          return _buildLoginSheet(height: height);
        },
      );
      if (!mounted) return;
      if (cookie == null || cookie.isEmpty) return; // 用户取消
    }

    int _epsCountOf(Map<String, dynamic>? vod) {
      // 中文注释：计算“当前选中线路”的分集数；若无线路则返回 0
      if (vod == null) return 0;
      final flags = (vod['seriesFlags'] as List?)?.cast<String>() ?? const [];
      if (flags.isEmpty) return 0;
      final seriesMap =
          (vod['seriesMap'] as Map?)?.cast<String, dynamic>() ?? const {};
      final eps = (seriesMap[flags.first] as List?)?.length ?? 0;
      return eps;
    }

    Future<Map<String, dynamic>?> _tryDetail({
      required List<String> links,
      required Map<String, String> pwds,
      required String tag,
    }) async {
      // 中文注释：打印一次尝试信息，避免换行字符串导致语法错误
      debugPrint(
        '[Detail] try detail(tag=$tag) links=${links.length}, pwds=${pwds.length}',
      );
      try {
        final res = await QuarkChannel.detail(links: links, pwds: pwds);
        debugPrint('[Detail] detail ok(tag=$tag) eps=${_epsCountOf(res)}');
        return res;
      } catch (e) {
        debugPrint('[Detail] detail error(tag=$tag): $e');
        return null;
      }
    }

    setState(() => _loadingVod = true);
    try {
      // 2) 先尝试当前分组
      Map<String, dynamic>? best = await _tryDetail(
        links: _quarkLinks,
        pwds: _quarkPwds,
        tag: _sources.isNotEmpty ? _sources[_selectedSourceIndex].name : '夸克当前',
      );
      int bestCnt = _epsCountOf(best);
      int bestIndex = _selectedSourceIndex; // 中文注释：记录“最优分组”的索引

      // 3) 若分集数 <= 1，自动尝试同页其他“夸克”分组
      if (bestCnt <= 1 && _sources.isNotEmpty) {
        for (int i = 0; i < _sources.length; i++) {
          final s = _sources[i];
          if (i == _selectedSourceIndex) continue; // 跳过已尝试的当前分组
          if (!s.type.contains('夸克')) continue; // 仅尝试夸克分组

          final links = s.links.map((e) => e.url).toList();
          final pwds = {
            for (final l in s.links)
              if ((l.pwd ?? '').isNotEmpty) l.url: l.pwd!,
          };
          final res = await _tryDetail(links: links, pwds: pwds, tag: s.name);
          final cnt = _epsCountOf(res);
          if (cnt > bestCnt) {
            best = res;
            bestCnt = cnt;
            bestIndex = i; // 记录为更优分组
            debugPrint(
              '[Detail] fallback pick better group: ${s.name} eps=$cnt',
            );
          }
        }
        if (bestCnt > 1 && bestIndex != _selectedSourceIndex) {
          // 自动切换 UI 选中的播放源，并同步链接/提取码集合
          setState(() {
            _selectedSourceIndex = bestIndex;
            _quarkLinks
              ..clear()
              ..addAll(_sources[bestIndex].links.map((e) => e.url));
            _quarkPwds
              ..clear()
              ..addAll({
                for (final l in _sources[bestIndex].links)
                  if ((l.pwd ?? '').isNotEmpty) l.url: l.pwd!,
              });
          });
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '已自动切换至更优夸克分组：${_sources[bestIndex].name}（$bestCnt 集）',
              ),
            ),
          );
        }
      }

      if (!mounted) return;
      setState(() {
        _quarkVod = best ?? const {};
        final flags =
            (_quarkVod?['seriesFlags'] as List?)?.cast<String>() ?? const [];
        _selectedFlag = flags.isNotEmpty ? flags.first : null;
        debugPrint('[Detail] final vod eps=$bestCnt flag=$_selectedFlag');
      });

      if (!mounted) return;
      setState(() {
        _quarkVod = best ?? const {};
        final flags =
            (_quarkVod?['seriesFlags'] as List?)?.cast<String>() ?? const [];
        _selectedFlag = flags.isNotEmpty ? flags.first : null;
        debugPrint('[Detail] final vod eps=$bestCnt flag=$_selectedFlag');
      });
    } finally {
      if (mounted) setState(() => _loadingVod = false);
    }
  }

  /// 播放：通过原生桥换取真实地址后跳播放器
  Future<void> _playViaQuark(String flag, String id) async {
    debugPrint('[Detail] play click flag=$flag id.len=${id.length}');
    try {
      final json = await QuarkChannel.playerContent(flag: flag, id: id);
      final url = (json['url'] as String?) ?? '';
      Map<String, String>? headers;
      final hdr = json['header'];
      if (hdr is Map) headers = hdr.cast<String, String>();
      if (!mounted) return;
      debugPrint(
        '[Detail] playerContent url.len=${url.length} headers=${headers?.keys}',
      );
      if (url.isEmpty) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('未获取到播放地址')));
        return;
      }
      
      // 构建选集列表和标题信息，与 detail_page.dart 保持一致
      final flags = (_quarkVod?['seriesFlags'] as List?)?.cast<String>() ?? const [];
      final seriesMap = (_quarkVod?['seriesMap'] as Map?)?.cast<String, dynamic>() ?? {};
      final selectedFlag = _selectedFlag ?? (flags.isNotEmpty ? flags.first : null);
      
      // 安全地转换选集列表，避免类型转换错误
      List<Map<String, dynamic>> episodes = const [];
      if (selectedFlag != null) {
        final rawEpisodes = seriesMap[selectedFlag] as List?;
        if (rawEpisodes != null) {
          episodes = rawEpisodes.map((e) {
            if (e is Map) {
              return Map<String, dynamic>.from(e);
            }
            return <String, dynamic>{};
          }).toList();
        }
      }

      // 构建播放标题
      final videoName = _videoArg?.name ?? '视频';
      String displayTitle = videoName;
      
      // 查找当前播放的集数索引
      int currentEpisodeIndex = -1;
      if (episodes.isNotEmpty) {
        currentEpisodeIndex = episodes.indexWhere((e) => (e['url'] as String?) == id);
        if (currentEpisodeIndex >= 0) {
          final episodeName = (episodes[currentEpisodeIndex]['name'] as String?)?.trim() ?? '';
          // 使用与 _buildEpisodes 中相同的命名逻辑
          final sharePattern = RegExp(r'^分享\d+ ?$');
          final finalEpisodeName = (episodeName.isEmpty || sharePattern.hasMatch(episodeName) || episodeName == '分享')
              ? '第${currentEpisodeIndex + 1}集'
              : episodeName;
          displayTitle = '$videoName - $finalEpisodeName';
        }
      }

      debugPrint('[Detail] 传递给播放器: 标题="$displayTitle", 选集数=${episodes.length}, 当前索引=$currentEpisodeIndex');

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (_) => PlayerPage(
            url: url, 
            headers: headers,
            title: displayTitle,
            episodes: episodes,
            initialEpisodeIndex: currentEpisodeIndex >= 0 ? currentEpisodeIndex : 0,
          ),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      debugPrint('[Detail] playerContent error: $e');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('获取播放地址失败：$e')));
    }
  }

  /// 构建扫码登录的大弹框（复用通用 WebView 作为内容）
  Widget _buildLoginSheet({required double height}) {
    return Container(
      height: height,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          // 头部栏
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              children: [
                const Icon(Icons.qr_code_2),
                const SizedBox(width: 6),
                const Text(
                  '夸克扫码登录',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop<String>(''),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          Expanded(
            child: ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(12),
              ),
              child: CloudDriveLoginWebView(
                title: '夸克扫码登录',
                delegate: QuarkLoginDelegate(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 小标签组件（中文注释）
  Widget _chip(String label, String value) {
    return Chip(
      label: Text('$label：$value'),
      padding: const EdgeInsets.symmetric(horizontal: 8),
    );
  }
}
