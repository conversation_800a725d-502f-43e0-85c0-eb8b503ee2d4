.class public interface abstract Lcom/github/catvod/spider/mergeguard/OoOoO0O0o0oOoO0O/oOoOoOo0O0O0oO0o;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract oOo0oOo0Oo0oO0Oo()Lcom/github/catvod/spider/mergeguard/oOoOo0O0Oo0o0OoO/oOoO0o0oOo0oO0Oo;
.end method

.method public abstract oOoOoOo0oOo0o0oO()Lcom/github/catvod/spider/mergeguard/OoOoO0O0o0oOoO0O/oOo0oOo0Oo0oO0Oo;
.end method

.method public abstract oOoOoOoOoOoOoO0o()Lcom/github/catvod/spider/mergeguard/OoOoO0O0o0oOoO0O/oOoOoOo0oOo0o0oO;
.end method
