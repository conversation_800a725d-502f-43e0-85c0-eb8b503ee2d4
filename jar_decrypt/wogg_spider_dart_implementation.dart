// // Wogg Spider的Dart实现
// // 基于AndroidCatVodSpider-multiThread项目的Wogg.java

// import 'dart:convert';
// import 'package:dio/dio.dart';
// import 'package:html/parser.dart' as html_parser;
// import 'package:html/dom.dart';

// /// Wogg Spider的Dart实现
// class WoggSpider {
//   // 网站配置 - 从原始Java代码中提取
//   final List<String> siteUrls = [
//     "https://www.wogg.net/",
//     "https://wogg.xxooo.cf/",
//     "https://wogg.xyz/"
//   ];
  
//   String _currentSiteUrl = "https://wogg.xxooo.cf/";
//   final Dio _dio = Dio();
  
//   // 正则表达式模式
//   final RegExp _regexCategory = RegExp(r'/vodtype/(\w+)\.html');
//   final RegExp _regexPageTotal = RegExp(r'\$\("\.mac_total"\)\.text\(\'(\d+)\'\);');
  
//   /// 初始化Spider
//   Future<void> init({Map<String, dynamic>? extend}) async {
//     // 配置HTTP客户端
//     _dio.options.headers = {
//       'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
//     };
    
//     // 如果有扩展配置，尝试不同的站点URL
//     if (extend != null && extend.containsKey('site')) {
//       final sites = extend['site'] as List<dynamic>?;
//       if (sites != null && sites.isNotEmpty) {
//         for (final site in sites) {
//           try {
//             final response = await _dio.get(site.toString());
//             if (response.data.toString().contains('电影')) {
//               _currentSiteUrl = site.toString();
//               break;
//             }
//           } catch (e) {
//             print('测试站点失败: $site, 错误: $e');
//           }
//         }
//       }
//     }
    
//     print('Wogg Spider初始化完成，使用站点: $_currentSiteUrl');
//   }
  
//   /// 获取首页内容和分类
//   Future<Map<String, dynamic>> homeContent({bool filter = false}) async {
//     try {
//       final response = await _dio.get(_currentSiteUrl);
//       final document = html_parser.parse(response.data);
      
//       // 解析分类
//       final classes = <Map<String, dynamic>>[];
//       final navLinks = document.querySelectorAll('.nav-link');
      
//       for (final link in navLinks) {
//         final href = link.attributes['href'] ?? '';
//         final match = _regexCategory.firstMatch(href);
//         if (match != null) {
//           classes.add({
//             'type_id': match.group(1),
//             'type_name': link.text.trim(),
//           });
//         }
//       }
      
//       // 解析首页视频列表
//       final vodList = _parseVodListFromDocument(document);
      
//       return {
//         'class': classes,
//         'list': vodList,
//       };
//     } catch (e) {
//       print('获取首页内容失败: $e');
//       return {'class': [], 'list': []};
//     }
//   }
  
//   /// 获取分类内容
//   Future<Map<String, dynamic>> categoryContent(
//     String tid,
//     String pg, {
//     bool filter = false,
//     Map<String, String>? extend,
//   }) async {
//     try {
//       // 构建URL参数
//       final urlParams = List<String>.filled(12, '');
//       urlParams[0] = tid; // 分类ID
//       urlParams[8] = pg;  // 页码
      
//       // 处理扩展参数
//       if (extend != null) {
//         extend.forEach((key, value) {
//           final index = int.tryParse(key);
//           if (index != null && index < urlParams.length) {
//             urlParams[index] = value;
//           }
//         });
//       }
      
//       final url = '$_currentSiteUrl/index.php/vodshow/${urlParams.join('-')}.html';
//       final response = await _dio.get(url);
//       final document = html_parser.parse(response.data);
      
//       // 解析分页信息
//       int page = int.tryParse(pg) ?? 1;
//       int limit = 72;
//       int total = 0;
      
//       final totalMatch = _regexPageTotal.firstMatch(response.data);
//       if (totalMatch != null) {
//         total = int.tryParse(totalMatch.group(1) ?? '0') ?? 0;
//       }
      
//       int count = total <= limit ? 1 : (total / limit).ceil();
      
//       // 解析视频列表
//       final vodList = _parseVodListFromDocument(document);
      
//       return {
//         'page': page,
//         'pagecount': count,
//         'limit': limit,
//         'total': total,
//         'list': vodList,
//       };
//     } catch (e) {
//       print('获取分类内容失败: $e');
//       return {
//         'page': 1,
//         'pagecount': 1,
//         'limit': 72,
//         'total': 0,
//         'list': [],
//       };
//     }
//   }
  
//   /// 获取视频详情
//   Future<Map<String, dynamic>> detailContent(List<String> ids) async {
//     if (ids.isEmpty) return {'list': []};
    
//     try {
//       final vodId = ids.first;
//       final url = '$_currentSiteUrl$vodId';
//       final response = await _dio.get(url);
//       final document = html_parser.parse(response.data);
      
//       // 解析视频信息
//       final vodInfo = <String, dynamic>{
//         'vod_id': vodId,
//         'vod_name': document.querySelector('.video-info-header .page-title')?.text ?? '',
//         'vod_pic': document.querySelector('.module-item-pic img')?.attributes['data-src'] ?? '',
//         'vod_area': document.querySelectorAll('.video-info-header a.tag-link').isNotEmpty 
//             ? document.querySelectorAll('.video-info-header a.tag-link').last.text 
//             : '',
//         'type_name': document.querySelectorAll('.video-info-header div.tag-link a')
//             .map((e) => e.text).join(','),
//       };
      
//       // 解析分享链接（阿里云盘等）
//       final shareLinks = document.querySelectorAll('.module-row-text')
//           .map((e) => e.attributes['data-clipboard-text'] ?? '')
//           .where((link) => link.isNotEmpty)
//           .map((link) => link.trim())
//           .toList();
      
//       // 处理播放源和播放链接
//       if (shareLinks.isNotEmpty) {
//         // 这里需要调用云盘解析服务
//         // 暂时返回原始链接，实际使用时需要解析为可播放链接
//         vodInfo['vod_play_from'] = shareLinks.asMap().entries
//             .map((entry) => '播放源${entry.key + 1}')
//             .join('\$\$\$');
        
//         vodInfo['vod_play_url'] = shareLinks.asMap().entries
//             .map((entry) => '第1集\$${entry.value}')
//             .join('\$\$\$');
//       }
      
//       // 解析其他详情信息
//       final infoItems = document.querySelectorAll('.video-info-item');
//       for (final item in infoItems) {
//         final title = item.previousElementSibling?.text ?? '';
//         if (title.contains('导演')) {
//           vodInfo['vod_director'] = item.querySelectorAll('a').map((e) => e.text).join(',');
//         } else if (title.contains('主演')) {
//           vodInfo['vod_actor'] = item.querySelectorAll('a').map((e) => e.text).join(',');
//         } else if (title.contains('年代')) {
//           vodInfo['vod_year'] = item.querySelector('a')?.text.trim() ?? '';
//         } else if (title.contains('备注')) {
//           vodInfo['vod_remarks'] = item.text.trim();
//         } else if (title.contains('剧情')) {
//           vodInfo['vod_content'] = item.querySelector('.sqjj_a')?.text
//               .replaceAll('[收起部分]', '').trim() ?? '';
//         }
//       }
      
//       return {'list': [vodInfo]};
//     } catch (e) {
//       print('获取视频详情失败: $e');
//       return {'list': []};
//     }
//   }
  
//   /// 搜索内容
//   Future<Map<String, dynamic>> searchContent(String key, {bool quick = false, String pg = '1'}) async {
//     try {
//       final encodedKey = Uri.encodeComponent(key);
//       final searchUrl = '$_currentSiteUrl/index.php/vodsearch/$encodedKey----------$pg---.html';
      
//       final response = await _dio.get(searchUrl);
//       final document = html_parser.parse(response.data);
      
//       final searchItems = document.querySelectorAll('.module-search-item');
//       final vodList = <Map<String, dynamic>>[];
      
//       for (final item in searchItems) {
//         final vodId = item.querySelector('.video-serial')?.attributes['href'] ?? '';
//         final name = item.querySelector('.video-serial')?.attributes['title'] ?? '';
//         final pic = item.querySelector('.module-item-pic img')?.attributes['data-src'] ?? '';
//         final remark = item.querySelector('.video-tag-icon')?.text ?? '';
        
//         if (vodId.isNotEmpty && name.isNotEmpty) {
//           vodList.add({
//             'vod_id': vodId,
//             'vod_name': name,
//             'vod_pic': pic,
//             'vod_remarks': remark,
//           });
//         }
//       }
      
//       return {'list': vodList};
//     } catch (e) {
//       print('搜索内容失败: $e');
//       return {'list': []};
//     }
//   }
  
//   /// 从文档解析视频列表
//   List<Map<String, dynamic>> _parseVodListFromDocument(Document document) {
//     final vodList = <Map<String, dynamic>>[];
//     final moduleItems = document.querySelectorAll('.module-item');
    
//     for (final item in moduleItems) {
//       final vodId = item.querySelector('.video-name a')?.attributes['href'] ?? '';
//       final vodPic = item.querySelector('.module-item-pic img')?.attributes['data-src'] ?? '';
//       final vodName = item.querySelector('.video-name')?.text ?? '';
//       final vodRemarks = item.querySelector('.module-item-text')?.text ?? '';
      
//       if (vodId.isNotEmpty && vodName.isNotEmpty) {
//         vodList.add({
//           'vod_id': vodId,
//           'vod_name': vodName,
//           'vod_pic': vodPic,
//           'vod_remarks': vodRemarks,
//         });
//       }
//     }
    
//     return vodList;
//   }
// }

// /// 使用示例
// void main() async {
//   final woggSpider = WoggSpider();
  
//   // 初始化
//   await woggSpider.init(extend: {
//     'site': ['https://www.wogg.net/', 'https://wogg.xxooo.cf/']
//   });
  
//   // 获取首页内容
//   final homeContent = await woggSpider.homeContent();
//   print('分类数量: ${homeContent['class'].length}');
//   print('首页视频数量: ${homeContent['list'].length}');
  
//   // 搜索测试
//   final searchResult = await woggSpider.searchContent('复仇者联盟');
//   print('搜索结果数量: ${searchResult['list'].length}');
// }
