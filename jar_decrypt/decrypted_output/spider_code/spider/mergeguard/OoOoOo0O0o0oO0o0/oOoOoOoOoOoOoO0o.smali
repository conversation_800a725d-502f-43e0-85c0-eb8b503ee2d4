.class public Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/io/Serializable;


# instance fields
.field public final oOo0oOo0Oo0oO0Oo:Ljava/lang/String;

.field public final oOoOoOo0oOo0o0oO:Ljava/lang/String;

.field public final oOoOoOoOoOoOoO0o:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .registers 3

    .line 1
    const-string v0, ""

    invoke-direct {p0, v0, p1}, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;)V
    .registers 4

    .line 2
    const-string v0, ""

    invoke-direct {p0, p1, p2, v0}, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .registers 5

    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    if-eqz p2, :cond_14

    .line 4
    const-string v0, ""

    if-nez p1, :cond_a

    move-object p1, v0

    :cond_a
    if-nez p3, :cond_d

    move-object p3, v0

    .line 5
    :cond_d
    iput-object p1, p0, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;->oOoOoOoOoOoOoO0o:Ljava/lang/String;

    .line 6
    iput-object p2, p0, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;->oOo0oOo0Oo0oO0Oo:Ljava/lang/String;

    .line 7
    iput-object p3, p0, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;->oOoOoOo0oOo0o0oO:Ljava/lang/String;

    return-void

    .line 8
    :cond_14
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "Local part not allowed to be null"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method


# virtual methods
.method public final equals(Ljava/lang/Object;)Z
    .registers 5

    .line 1
    const/4 v0, 0x0

    .line 2
    if-nez p1, :cond_4

    .line 3
    .line 4
    return v0

    .line 5
    :cond_4
    instance-of v1, p1, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;

    .line 6
    .line 7
    if-nez v1, :cond_9

    .line 8
    .line 9
    return v0

    .line 10
    :cond_9
    check-cast p1, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;

    .line 11
    .line 12
    iget-object v1, p0, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;->oOo0oOo0Oo0oO0Oo:Ljava/lang/String;

    .line 13
    .line 14
    iget-object v2, p1, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;->oOo0oOo0Oo0oO0Oo:Ljava/lang/String;

    .line 15
    .line 16
    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    if-eqz v1, :cond_21

    .line 21
    .line 22
    iget-object v1, p0, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;->oOoOoOoOoOoOoO0o:Ljava/lang/String;

    .line 23
    .line 24
    iget-object p1, p1, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;->oOoOoOoOoOoOoO0o:Ljava/lang/String;

    .line 25
    .line 26
    invoke-virtual {v1, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 27
    .line 28
    .line 29
    move-result p1

    .line 30
    if-eqz p1, :cond_21

    .line 31
    .line 32
    const/4 p1, 0x1

    .line 33
    return p1

    .line 34
    :cond_21
    return v0
    .line 35
    .line 36
    .line 37
    .line 38
    .line 39
    .line 40
    .line 41
    .line 42
    .line 43
    .line 44
    .line 45
    .line 46
    .line 47
    .line 48
    .line 49
    .line 50
    .line 51
    .line 52
    .line 53
    .line 54
    .line 55
    .line 56
    .line 57
    .line 58
    .line 59
    .line 60
    .line 61
    .line 62
    .line 63
    .line 64
    .line 65
    .line 66
    .line 67
    .line 68
    .line 69
    .line 70
    .line 71
    .line 72
    .line 73
    .line 74
    .line 75
    .line 76
    .line 77
    .line 78
    .line 79
    .line 80
    .line 81
    .line 82
    .line 83
    .line 84
    .line 85
    .line 86
    .line 87
    .line 88
    .line 89
    .line 90
    .line 91
    .line 92
    .line 93
    .line 94
    .line 95
    .line 96
    .line 97
    .line 98
    .line 99
    .line 100
    .line 101
    .line 102
    .line 103
    .line 104
    .line 105
    .line 106
    .line 107
    .line 108
    .line 109
    .line 110
    .line 111
    .line 112
    .line 113
    .line 114
.end method

.method public final hashCode()I
    .registers 3

    .line 1
    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;->oOoOoOoOoOoOoO0o:Ljava/lang/String;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget-object v1, p0, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;->oOo0oOo0Oo0oO0Oo:Ljava/lang/String;

    .line 8
    .line 9
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    xor-int/2addr v0, v1

    .line 14
    return v0
    .line 15
    .line 16
    .line 17
    .line 18
    .line 19
    .line 20
    .line 21
    .line 22
    .line 23
    .line 24
    .line 25
    .line 26
    .line 27
    .line 28
    .line 29
    .line 30
    .line 31
    .line 32
    .line 33
    .line 34
    .line 35
    .line 36
    .line 37
    .line 38
    .line 39
    .line 40
    .line 41
    .line 42
    .line 43
    .line 44
    .line 45
    .line 46
    .line 47
    .line 48
    .line 49
    .line 50
    .line 51
    .line 52
    .line 53
    .line 54
    .line 55
    .line 56
    .line 57
    .line 58
    .line 59
    .line 60
    .line 61
    .line 62
    .line 63
    .line 64
    .line 65
    .line 66
.end method

.method public final toString()Ljava/lang/String;
    .registers 5

    .line 1
    const-string v0, ""

    .line 2
    .line 3
    iget-object v1, p0, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;->oOoOoOoOoOoOoO0o:Ljava/lang/String;

    .line 4
    .line 5
    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    iget-object v2, p0, Lcom/github/catvod/spider/mergeguard/OoOoOo0O0o0oO0o0/oOoOoOoOoOoOoO0o;->oOo0oOo0Oo0oO0Oo:Ljava/lang/String;

    .line 10
    .line 11
    if-eqz v0, :cond_d

    .line 12
    .line 13
    return-object v2

    .line 14
    :cond_d
    new-instance v0, Ljava/lang/StringBuffer;

    .line 15
    .line 16
    const-string v3, "{"

    .line 17
    .line 18
    invoke-direct {v0, v3}, Ljava/lang/StringBuffer;-><init>(Ljava/lang/String;)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    .line 22
    .line 23
    .line 24
    const-string v1, "}"

    .line 25
    .line 26
    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    .line 27
    .line 28
    .line 29
    invoke-virtual {v0, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    .line 30
    .line 31
    .line 32
    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    return-object v0
    .line 37
    .line 38
    .line 39
    .line 40
    .line 41
    .line 42
    .line 43
    .line 44
    .line 45
    .line 46
    .line 47
    .line 48
    .line 49
    .line 50
    .line 51
    .line 52
    .line 53
    .line 54
    .line 55
    .line 56
    .line 57
    .line 58
    .line 59
    .line 60
    .line 61
    .line 62
    .line 63
    .line 64
    .line 65
    .line 66
.end method
