.class public abstract Lcom/github/catvod/spider/mergeguard/oOoOo0O0Oo0o0OoO/oOoOoOo0oOo0o0oO;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method private constructor <init>()V
    .registers 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lcom/github/catvod/spider/mergeguard/oOoOo0O0Oo0o0OoO/oOo0oOo0Oo0oO0Oo;)V
    .registers 2

    .line 2
    invoke-direct {p0}, Lcom/github/catvod/spider/mergeguard/oOoOo0O0Oo0o0OoO/oOoOoOo0oOo0o0oO;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract oOoOoOoOoOoOoO0o()Ljava/lang/ClassLoader;
.end method
