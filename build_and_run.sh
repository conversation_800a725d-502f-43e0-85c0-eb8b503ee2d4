#!/bin/bash

# 夸克播放修复 - 构建和运行脚本
# 用法: ./build_and_run.sh

echo "🔧 开始构建夸克播放修复版本..."

# 清理之前的构建
echo "📁 清理构建缓存..."
flutter clean

# 获取依赖
echo "📦 获取依赖包..."
flutter pub get

# 构建macOS应用
echo "🍎 构建macOS应用..."
flutter build macos --release

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    echo ""
    echo "🚀 启动应用进行测试..."
    echo "请检查以下修复效果："
    echo "  1. 夸克CDN认证增强"
    echo "  2. 播放策略优化（直链优先）"
    echo "  3. Cookie验证增强"
    echo "  4. 错误处理改进"
    echo ""
    
    # 启动应用
    open build/macos/Build/Products/Release/free_tv_player.app
    
    echo "📋 测试步骤："
    echo "  1. 确保夸克Cookie已设置"
    echo "  2. 选择wogg数据源的视频"
    echo "  3. 点击播放，观察是否还有403错误"
    echo "  4. 查看日志中的认证信息"
else
    echo "❌ 构建失败，请检查错误信息"
    exit 1
fi