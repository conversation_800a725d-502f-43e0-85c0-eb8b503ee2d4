// // Spider管理器 - 统一管理所有Spider实现
// // 替代JAR包中的Init和BaseSpiderGuard功能

// import 'dart:convert';
// import 'wogg_spider_dart_implementation.dart';

// /// Spider基础接口
// abstract class Spider {
//   Future<void> init({Map<String, dynamic>? extend});
//   Future<Map<String, dynamic>> homeContent({bool filter = false});
//   Future<Map<String, dynamic>> categoryContent(String tid, String pg, {bool filter = false, Map<String, String>? extend});
//   Future<Map<String, dynamic>> detailContent(List<String> ids);
//   Future<Map<String, dynamic>> searchContent(String key, {bool quick = false, String pg = '1'});
//   Future<Map<String, dynamic>> playerContent(String flag, String id, List<String> vipFlags);
// }

// /// Spider管理器 - 相当于JAR包中的Init类
// class SpiderManager {
//   static final SpiderManager _instance = SpiderManager._internal();
//   factory SpiderManager() => _instance;
//   SpiderManager._internal();
  
//   final Map<String, Spider Function()> _spiderFactories = {};
//   final Map<String, Spider> _spiderInstances = {};
  
//   /// 初始化Spider管理器
//   void init() {
//     // 注册所有可用的Spider
//     _registerSpiders();
//   }
  
//   /// 注册Spider工厂方法
//   void _registerSpiders() {
//     // 注册Wogg Spider
//     _spiderFactories['csp_Wogg'] = () => WoggSpiderAdapter();
//     _spiderFactories['csp_WoggGuard'] = () => WoggSpiderAdapter();
    
//     // 可以继续注册其他Spider
//     // _spiderFactories['csp_Bili'] = () => BiliSpiderAdapter();
//     // _spiderFactories['csp_Douban'] = () => DoubanSpiderAdapter();
//   }
  
//   /// 获取Spider实例 - 相当于JAR包中的getSpider方法
//   Spider? getSpider(String className) {
//     // 如果已经有实例，直接返回
//     if (_spiderInstances.containsKey(className)) {
//       return _spiderInstances[className];
//     }
    
//     // 创建新实例
//     final factory = _spiderFactories[className];
//     if (factory != null) {
//       final spider = factory();
//       _spiderInstances[className] = spider;
//       return spider;
//     }
    
//     print('未找到Spider: $className');
//     return null;
//   }
  
//   /// 获取所有可用的Spider列表
//   List<String> getAvailableSpiders() {
//     return _spiderFactories.keys.toList();
//   }
  
//   /// 清理Spider实例
//   void clearInstances() {
//     _spiderInstances.clear();
//   }
// }

// /// Wogg Spider适配器 - 相当于JAR包中的WoggGuard
// class WoggSpiderAdapter implements Spider {
//   final WoggSpider _woggSpider = WoggSpider();
  
//   @override
//   Future<void> init({Map<String, dynamic>? extend}) async {
//     await _woggSpider.init(extend: extend);
//   }
  
//   @override
//   Future<Map<String, dynamic>> homeContent({bool filter = false}) async {
//     return await _woggSpider.homeContent(filter: filter);
//   }
  
//   @override
//   Future<Map<String, dynamic>> categoryContent(
//     String tid, 
//     String pg, {
//     bool filter = false, 
//     Map<String, String>? extend
//   }) async {
//     return await _woggSpider.categoryContent(tid, pg, filter: filter, extend: extend);
//   }
  
//   @override
//   Future<Map<String, dynamic>> detailContent(List<String> ids) async {
//     return await _woggSpider.detailContent(ids);
//   }
  
//   @override
//   Future<Map<String, dynamic>> searchContent(
//     String key, {
//     bool quick = false, 
//     String pg = '1'
//   }) async {
//     return await _woggSpider.searchContent(key, quick: quick, pg: pg);
//   }
  
//   @override
//   Future<Map<String, dynamic>> playerContent(
//     String flag, 
//     String id, 
//     List<String> vipFlags
//   ) async {
//     // 这里需要实现播放链接解析
//     // 对于阿里云盘等分享链接，需要调用相应的解析服务
//     return {
//       'parse': 0,
//       'playUrl': '',
//       'url': id, // 暂时返回原始链接
//     };
//   }
// }

// /// 使用示例和测试
// class SpiderTest {
//   static Future<void> testWoggSpider() async {
//     final manager = SpiderManager();
//     manager.init();
    
//     print('=== Spider管理器测试 ===');
//     print('可用Spider: ${manager.getAvailableSpiders()}');
    
//     // 获取Wogg Spider
//     final spider = manager.getSpider('csp_Wogg');
//     if (spider == null) {
//       print('获取Spider失败');
//       return;
//     }
    
//     try {
//       // 初始化
//       await spider.init(extend: {
//         'site': ['https://wogg.xxooo.cf/', 'https://www.wogg.net/']
//       });
      
//       print('\n=== 测试首页内容 ===');
//       final homeContent = await spider.homeContent();
//       final classes = homeContent['class'] as List<dynamic>;
//       final homeList = homeContent['list'] as List<dynamic>;
      
//       print('分类数量: ${classes.length}');
//       if (classes.isNotEmpty) {
//         print('前3个分类:');
//         for (int i = 0; i < 3 && i < classes.length; i++) {
//           final cls = classes[i] as Map<String, dynamic>;
//           print('  ${cls['type_name']} (${cls['type_id']})');
//         }
//       }
      
//       print('首页视频数量: ${homeList.length}');
//       if (homeList.isNotEmpty) {
//         final firstVideo = homeList.first as Map<String, dynamic>;
//         print('第一个视频: ${firstVideo['vod_name']}');
//       }
      
//       // 测试分类内容
//       if (classes.isNotEmpty) {
//         print('\n=== 测试分类内容 ===');
//         final firstClass = classes.first as Map<String, dynamic>;
//         final categoryContent = await spider.categoryContent(
//           firstClass['type_id'], 
//           '1'
//         );
        
//         final categoryList = categoryContent['list'] as List<dynamic>;
//         print('分类"${firstClass['type_name']}"视频数量: ${categoryList.length}');
        
//         // 测试视频详情
//         if (categoryList.isNotEmpty) {
//           print('\n=== 测试视频详情 ===');
//           final firstVideo = categoryList.first as Map<String, dynamic>;
//           final detailContent = await spider.detailContent([firstVideo['vod_id']]);
          
//           final detailList = detailContent['list'] as List<dynamic>;
//           if (detailList.isNotEmpty) {
//             final detail = detailList.first as Map<String, dynamic>;
//             print('视频详情:');
//             print('  名称: ${detail['vod_name']}');
//             print('  演员: ${detail['vod_actor'] ?? '未知'}');
//             print('  导演: ${detail['vod_director'] ?? '未知'}');
//             print('  播放源: ${detail['vod_play_from'] ?? '无'}');
//           }
//         }
//       }
      
//       // 测试搜索
//       print('\n=== 测试搜索功能 ===');
//       final searchResult = await spider.searchContent('复仇者联盟');
//       final searchList = searchResult['list'] as List<dynamic>;
//       print('搜索"复仇者联盟"结果数量: ${searchList.length}');
      
//       if (searchList.isNotEmpty) {
//         print('前3个搜索结果:');
//         for (int i = 0; i < 3 && i < searchList.length; i++) {
//           final video = searchList[i] as Map<String, dynamic>;
//           print('  ${video['vod_name']} - ${video['vod_remarks']}');
//         }
//       }
      
//     } catch (e) {
//       print('测试过程中出现错误: $e');
//     }
//   }
// }

// /// 主函数 - 运行测试
// void main() async {
//   await SpiderTest.testWoggSpider();
// }
