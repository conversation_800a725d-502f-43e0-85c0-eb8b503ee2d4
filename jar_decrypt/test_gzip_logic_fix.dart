#!/usr/bin/env dart

// 测试gzip逻辑修复
// 运行命令: dart run test_gzip_logic_fix.dart

import 'dart:convert';
import 'dart:io';

void main() async {
  print('🧪 测试gzip检测逻辑修复');
  print('=' * 50);
  
  // 测试数据
  final testCases = [
    {
      'name': '正常HTML内容',
      'data': '<!DOCTYPE html><html><head><title>测试</title></head><body>电影</body></html>',
      'expected': 'HTML内容',
    },
    {
      'name': '真正的gzip数据',
      'data': String.fromCharCodes([0x1f, 0x8b, 0x08, 0x00, 0x01, 0x02, 0x03]),
      'expected': 'gzip数据',
    },
    {
      'name': '大量控制字符的二进制数据',
      'data': String.fromCharCodes([0x5b, 0xfffd, 0x02, 0x3b, 0x23, 0x59, 0x6f, 0xfffd] + List.filled(100, 0x01)),
      'expected': '二进制数据',
    },
  ];
  
  for (final testCase in testCases) {
    print('\n📋 测试: ${testCase['name']}');
    print('-' * 30);
    
    final data = testCase['data'] as String;
    final expected = testCase['expected'] as String;
    
    print('数据长度: ${data.length}');
    
    // 检查是否是gzip数据
    final isGzip = data.isNotEmpty &&
        data.codeUnitAt(0) == 0x1f &&
        data.length > 1 &&
        data.codeUnitAt(1) == 0x8b;
    
    print('gzip魔数检测: ${isGzip ? '是' : '否'}');
    
    // 检查是否包含HTML
    final isHtml = data.contains('<!DOCTYPE html') || data.contains('<html');
    print('HTML内容检测: ${isHtml ? '是' : '否'}');
    
    // 检查是否包含大量控制字符
    final controlCharCount = data.codeUnits
        .where((code) => code < 32 && code != 9 && code != 10 && code != 13)
        .length;
    final controlCharRatio = controlCharCount / data.length;
    final hasBinaryData = controlCharRatio > 0.1;
    
    print('控制字符比例: ${(controlCharRatio * 100).toStringAsFixed(1)}%');
    print('二进制数据检测: ${hasBinaryData ? '是' : '否'}');
    
    // 检查中文内容
    final hasChinese = data.contains('电影') || data.contains('电视剧') || data.contains('综艺');
    print('中文内容检测: ${hasChinese ? '是' : '否'}');
    
    // 显示前10个字节
    final firstBytes = data.codeUnits.take(10)
        .map((e) => '0x${e.toRadixString(16).padLeft(2, '0')}')
        .join(' ');
    print('前10个字节: $firstBytes');
    
    // 判断结果
    String result;
    if (isGzip) {
      result = 'gzip数据';
    } else if (isHtml) {
      result = 'HTML内容';
    } else if (hasBinaryData) {
      result = '二进制数据';
    } else {
      result = '未知内容';
    }
    
    print('检测结果: $result');
    print('预期结果: $expected');
    print('结果匹配: ${result == expected ? '✅' : '❌'}');
  }
  
  print('\n🎉 测试完成！');
  print('\n💡 关键要点:');
  print('  1. gzip魔数检测：检查前两个字节是否为0x1f 0x8b');
  print('  2. HTML内容检测：查找<!DOCTYPE html>或<html>标签');
  print('  3. 二进制数据检测：控制字符比例超过10%');
  print('  4. 优先级：gzip魔数 > HTML内容 > 二进制数据');
  
  print('\n🔧 修复说明:');
  print('  - 如果检测到gzip魔数，说明Dio没有自动解压，需要手动处理');
  print('  - 如果检测到HTML内容，说明Dio已经自动解压，数据正常');
  print('  - Content-Encoding头只是提示，实际数据内容才是判断依据');
}
