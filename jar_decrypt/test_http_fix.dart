#!/usr/bin/env dart

// 测试HTTP修复
// 运行命令: dart run test_http_fix.dart

import 'dart:convert';
import 'dart:io';

void main() async {
  print('🧪 测试HTTP gzip处理');
  print('=' * 50);
  
  // 测试1: 直接使用HttpClient测试gzip
  print('\n📋 测试1: 使用Dart HttpClient测试gzip处理');
  
  try {
    final client = HttpClient();
    
    // 设置请求头
    final request = await client.getUrl(Uri.parse('https://wogg.xxooo.cf'));
    request.headers.set('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    request.headers.set('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8');
    request.headers.set('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8');
    // 不设置Accept-Encoding，让HttpClient自动处理
    
    print('📤 发送请求到: https://wogg.xxooo.cf');
    print('📋 请求头:');
    request.headers.forEach((name, values) {
      print('  $name: ${values.join(', ')}');
    });
    
    final response = await request.close();
    
    print('\n📥 响应信息:');
    print('  状态码: ${response.statusCode}');
    print('  Content-Type: ${response.headers.value('content-type')}');
    print('  Content-Encoding: ${response.headers.value('content-encoding')}');
    print('  Content-Length: ${response.headers.value('content-length')}');
    
    // 读取响应体
    final responseBody = await response.transform(utf8.decoder).join();
    
    print('  响应体长度: ${responseBody.length}');
    
    // 检查响应内容
    if (responseBody.isEmpty) {
      print('❌ 响应体为空');
    } else if (responseBody.contains('<!DOCTYPE html') || responseBody.contains('<html')) {
      print('✅ 收到HTML内容');
      
      // 检查是否包含中文
      if (responseBody.contains('电影') || responseBody.contains('电视剧') || responseBody.contains('综艺')) {
        print('✅ 中文内容正确显示');
      } else {
        print('⚠️  未检测到中文内容');
      }
      
      // 显示前200个字符
      final preview = responseBody.length > 200 ? responseBody.substring(0, 200) : responseBody;
      print('📄 内容预览:');
      print(preview);
      
    } else {
      // 检查是否是二进制数据
      final controlCharCount = responseBody.codeUnits
          .where((code) => code < 32 && code != 9 && code != 10 && code != 13)
          .length;
      final controlCharRatio = controlCharCount / responseBody.length;
      
      if (controlCharRatio > 0.1) {
        print('❌ 收到大量控制字符 (${(controlCharRatio * 100).toStringAsFixed(1)}%)');
        print('这可能是未正确处理的二进制数据');
        
        // 检查是否是gzip数据
        if (responseBody.isNotEmpty && 
            responseBody.codeUnitAt(0) == 0x1f && 
            responseBody.length > 1 && 
            responseBody.codeUnitAt(1) == 0x8b) {
          print('🗜️  检测到gzip魔数，尝试手动解压...');
          
          try {
            final bytes = responseBody.codeUnits.map((e) => e & 0xFF).toList();
            final decompressed = gzip.decode(bytes);
            final decodedString = utf8.decode(decompressed);
            
            print('✅ 手动解压成功！');
            print('  原始长度: ${responseBody.length}');
            print('  解压后长度: ${decodedString.length}');
            
            if (decodedString.contains('<!DOCTYPE html') || decodedString.contains('<html')) {
              print('✅ 解压后是HTML内容');
              
              if (decodedString.contains('电影') || decodedString.contains('电视剧')) {
                print('✅ 解压后中文内容正确');
              }
            }
            
          } catch (e) {
            print('❌ 手动解压失败: $e');
          }
        }
      } else {
        print('⚠️  收到未知格式的文本内容');
      }
    }
    
    client.close();
    
  } catch (e) {
    print('❌ 请求失败: $e');
  }
  
  print('\n🎉 测试完成！');
  print('\n💡 分析结果:');
  print('  如果看到"收到HTML内容"和"中文内容正确显示"，说明HTTP客户端工作正常');
  print('  如果看到"检测到gzip魔数"和"手动解压成功"，说明需要修复Dio配置');
  print('  如果看到大量控制字符，说明存在编码问题');
}
