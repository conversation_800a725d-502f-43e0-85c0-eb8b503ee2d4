#!/bin/bash

# 夸克TS文件400错误修复测试脚本
# 基于AndroidCatVodSpider成功实现的浏览器指纹头部修复

echo "====== 夸克TS文件400错误修复测试 ======"
echo "本次修复包含以下关键改进："
echo ""
echo "1. 添加了关键的浏览器指纹头部："
echo "   - Sec-Ch-Ua: \"Chromium\";v=\"100\", \"Not(A:Brand\";v=\"8\", \"Google Chrome\";v=\"100\""
echo "   - Sec-Ch-Ua-Mobile: ?0"
echo "   - Sec-Ch-Ua-Platform: \"Windows\""
echo "   - Sec-Fetch-Dest: video (TS文件) / empty (M3U8文件)"
echo "   - Sec-Fetch-Mode: cors"
echo "   - Sec-Fetch-Site: cross-site"
echo ""
echo "2. 修正了Origin格式："
echo "   - 修改前: https://pan.quark.cn/ (带尾斜杠)"
echo "   - 修改后: https://pan.quark.cn (不带尾斜杠)"
echo ""
echo "3. 简化了Accept头部："
echo "   - M3U8文件: 从特殊化修改为 */*"
echo "   - TS文件: 添加 */* 和 Accept-Language"
echo ""
echo "4. 增强了Host头部处理："
echo "   - 对夸克CDN的TS和M3U8文件明确移除Host和Content-Type头部"
echo ""
echo "5. 所有夸克API请求也添加了浏览器指纹头部"
echo ""
echo "====== 请重新编译并测试 ======"
echo "1. 重新运行应用"
echo "2. 尝试播放之前400错误的夸克视频"
echo "3. 观察控制台日志中的请求头部变化"
echo "4. 确认TS文件是否能正常加载"
echo ""
echo "预期效果："
echo "- TS文件请求应该返回200/206状态码而不是400"
echo "- 视频应该能够正常播放而不是卡在加载"
echo "- 控制台应该看到新增的Sec-Fetch-*头部"
echo ""
echo "如果仍有问题，请检查："
echo "- 夸克Cookie是否仍然有效"
echo "- 网络连接是否正常"
echo "- 控制台是否有其他错误信息"
