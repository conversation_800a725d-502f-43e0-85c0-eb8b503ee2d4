.class public final synthetic Lcom/github/catvod/spider/mergeguard/oOoO0o0oOo0oO0Oo/oOo0oOo0Oo0oO0Oo;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/whl/quickjs/wrapper/JSCallFunction;


# instance fields
.field public final synthetic oOo0oOo0Oo0oO0Oo:Ljava/lang/Object;

.field public final synthetic oOoOoOoOoOoOoO0o:Ljava/lang/reflect/Method;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/reflect/Method;Ljava/lang/Object;)V
    .registers 3

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/github/catvod/spider/mergeguard/oOoO0o0oOo0oO0Oo/oOo0oOo0Oo0oO0Oo;->oOoOoOoOoOoOoO0o:Ljava/lang/reflect/Method;

    iput-object p2, p0, Lcom/github/catvod/spider/mergeguard/oOoO0o0oOo0oO0Oo/oOo0oOo0Oo0oO0Oo;->oOo0oOo0Oo0oO0Oo:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public final call([Ljava/lang/Object;)Ljava/lang/Object;
    .registers 4

    .line 1
    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/oOoO0o0oOo0oO0Oo/oOo0oOo0Oo0oO0Oo;->oOoOoOoOoOoOoO0o:Ljava/lang/reflect/Method;

    iget-object v1, p0, Lcom/github/catvod/spider/mergeguard/oOoO0o0oOo0oO0Oo/oOo0oOo0Oo0oO0Oo;->oOo0oOo0Oo0oO0Oo:Ljava/lang/Object;

    invoke-static {v0, v1, p1}, Lcom/whl/quickjs/wrapper/QuickJSObject;->oOoOoOoOoOoOoO0o(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
