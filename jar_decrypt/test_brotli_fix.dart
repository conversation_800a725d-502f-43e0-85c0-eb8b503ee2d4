#!/usr/bin/env dart

// 测试Brotli修复
// 运行命令: dart run test_brotli_fix.dart

import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';

void main() async {
  print('🧪 测试Brotli压缩处理修复');
  print('=' * 50);
  
  // 测试不同的Accept-Encoding设置
  final testCases = [
    {
      'name': '只请求gzip和deflate',
      'acceptEncoding': 'gzip, deflate',
    },
    {
      'name': '请求所有压缩格式',
      'acceptEncoding': 'gzip, deflate, br',
    },
    {
      'name': '不设置Accept-Encoding',
      'acceptEncoding': null,
    },
  ];
  
  for (final testCase in testCases) {
    print('\n📋 测试: ${testCase['name']}');
    print('-' * 30);
    
    try {
      final dio = Dio(
        BaseOptions(
          connectTimeout: const Duration(milliseconds: 10000),
          receiveTimeout: const Duration(milliseconds: 30000),
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            if (testCase['acceptEncoding'] != null)
              'Accept-Encoding': testCase['acceptEncoding'] as String,
          },
          responseType: ResponseType.plain,
          followRedirects: true,
        ),
      );
      
      final response = await dio.get('https://wogg.xxooo.cf');
      
      print('📡 响应信息:');
      print('  状态码: ${response.statusCode}');
      print('  Content-Type: ${response.headers.value('content-type')}');
      print('  Content-Encoding: ${response.headers.value('content-encoding')}');
      print('  数据类型: ${response.data.runtimeType}');
      
      if (response.data is String) {
        final data = response.data as String;
        print('  响应长度: ${data.length}');
        
        // 检查是否是HTML
        if (data.contains('<!DOCTYPE html') || data.contains('<html')) {
          print('✅ 收到HTML内容');
          
          if (data.contains('电影') || data.contains('电视剧')) {
            print('✅ 中文内容正确显示');
          } else {
            print('⚠️  未检测到中文内容');
          }
        } else {
          // 检查是否是二进制数据
          final controlCharCount = data.codeUnits
              .where((code) => code < 32 && code != 9 && code != 10 && code != 13)
              .length;
          final controlCharRatio = controlCharCount / data.length;
          
          if (controlCharRatio > 0.1) {
            print('❌ 检测到大量控制字符 (${(controlCharRatio * 100).toStringAsFixed(1)}%)');
            print('这可能是未正确处理的压缩数据');
            
            // 显示前20个字节的十六进制
            final firstBytes = data.codeUnits.take(20)
                .map((e) => '0x${e.toRadixString(16).padLeft(2, '0')}')
                .join(' ');
            print('前20个字节: $firstBytes');
          } else {
            print('⚠️  收到文本内容但不是HTML');
            final preview = data.length > 100 ? data.substring(0, 100) : data;
            print('内容预览: $preview');
          }
        }
      }
      
      dio.close();
      
    } catch (e) {
      print('❌ 请求失败: $e');
    }
    
    // 等待一下再进行下一个测试
    await Future.delayed(const Duration(seconds: 2));
  }
  
  print('\n🎉 测试完成！');
  print('\n💡 分析结果:');
  print('  如果"只请求gzip和deflate"的测试显示"✅ 中文内容正确显示"，说明修复成功');
  print('  如果仍然显示"❌ 检测到大量控制字符"，说明服务器强制使用Brotli');
  print('  如果"不设置Accept-Encoding"的测试正常，说明Dio的默认配置有问题');
  
  print('\n🔧 建议:');
  print('  1. 如果只有"只请求gzip和deflate"正常，在代码中明确设置Accept-Encoding');
  print('  2. 如果都不正常，可能需要使用原生HttpClient替代Dio');
  print('  3. 如果服务器强制Brotli，需要添加Brotli解压库');
}
