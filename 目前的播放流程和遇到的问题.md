### 播放流程（macOS 当前实现）

- **入口**
    - Flutter 通过 MethodChannel `free_tv/quark` 调 `playerContent`，入参：`id`（`fid++token++shareId++stoken` 或直链）、`flag`（清晰度）。
    - 如 `id` 为直链 `.mp4/.m3u8`：直接返回该 URL（不走转存逻辑）。

- **登录与 Cookie**
    - `init(cookie)` 将夸克登录 `Cookie` 存在 `UserDefaults`（键：`free_tv_quark_cookie`）。
    - 每次调用夸克 PC 接口：
        - 发送最小必要头：`User-Agent`、`Referer: https://pan.quark.cn/`、`Cookie`；不显式设 `Host`。
        - `httpShouldHandleCookies = false`；手动合并响应 `Set-Cookie` 到本地存储，并注入到 `HTTPCookieStorage`（便于 AVPlayer 访问 CDN）。
        - 401 时会先调用一次分享详情“唤醒 Cookie”，再重试。

- **资源解析与转存**
    - 解析 `id` 得到 `fid/fileToken/shareId/stoken`。
    - 校验登录状态，失败则尝试“唤醒 Cookie”。
    - 将分享文件转存到个人盘根目录 `temp/`（若无则创建），轮询保存任务拿到个人盘 `selfFid`（带本地缓存避免重复保存）。
    - 生成播放链接优先级：
        - 1) 调用 `file/v2/play` 拿转码 m3u8（按 `flag` 匹配 4k/2k/超清/高清/普画，兜底任意）。
        - 2) 失败则 `file/download` 拿直链（通常为 mp4）。
    - 对最终 URL 做“加强清洗”：去 CR/LF、裁剪尾部空白及可疑碎片，仅将空格替换为 `%20`，避免二次编码破坏已存在 `%XX`。

- **代理层（本地 127.0.0.1:9999）**
    - 启动 `NWListener`（端口 `9999`），将真实播放 URL 与请求头映射为一个 `key`，生成 `http://127.0.0.1:9999/proxy?key=...`。
    - Flutter 播放器只拿到这个「代理 URL」，请求头在代理层注入，Flutter 侧 `header` 返回为空。

- **代理请求策略（关键防 412/防盗链）**
    - 通用：
        - 透传 Method（GET/HEAD），不手动设置 `Host`，不带 Cookie 除非明确需要。
        - 打印与调试日志全面记录请求与响应。
    - **m3u8 请求**：
        - 强制 `Accept-Encoding: identity`。
        - 添加 `Origin: https://pan.quark.cn`。
        - 设置 `Accept: application/x-mpegURL,application/vnd.apple.mpegurl,*/*`。
        - 忽略上游携带的 `Range`（避免 412/416）。
    - **.ts 分片请求**：
        - 强制移除 `Cookie`（减少 CDN 鉴权差异导致 4xx）。
        - 允许透传 `Range`。
    - 其他：
        - 复制关键响应头：`Content-Type`、`Content-Range`、`Accept-Ranges`、`Cache-Control`。
        - 重新计算 `Content-Length`；附加通用 CORS 头。

- **m3u8 重写**
    - 当响应为 m3u8：
        - 以通用换行分割并 `trim`，清理 CR/LF 与空白后多余碎片。
        - 对每个非注释行：
            - 若是 TS 相对路径，按 m3u8 基址拼成绝对 URL。
            - 将每个 TS URL 再写入代理（为 TS 单独去 Cookie）并替换原行。
        - 结果重新组装并返回。

- **返回给 Flutter**
    - `playerContent` 返回：
        - `url`: 代理 URL（127.0.0.1:9999/proxy?key=...）
        - `header`: `{}`（空；header 由代理统一注入）
        - `parse`: `"0"`

### 关键文件/位置

- `macos/Runner/MainFlutterWindow.swift`
    - 方法通道：`free_tv/quark` 的 `init`/`detail`/`playerContent`
    - 代理类：`ProxyServer`（端口 9999）
    - m3u8 重写：`modifyM3U8Content(...)`
    - 代理响应发送：`sendProxyResponse(...)`
    - 代理转发请求：`handleProxyRequest(...)`

### 目前的问题日志

2025-08-17 10:37:16.781 free_tv_player[53159:8374642] [Quark.macOS] 代理URL: http://127.0.0.1:9999/proxy?key=0247df5bf039c112d341b8b509b8b7cb
flutter: [QuarkChannel.playerContent] got url?=true, header.keys=[]
flutter: [Detail] playerContent url.len=64 headers=()
flutter: [Detail] episodes flag=quark原画
flutter: [Detail] episodes count=11
2025-08-17 10:37:16.793 free_tv_player[53159:8378677] [ProxyServer] 收到请求: GET /proxy?key=0247df5bf039c112d341b8b509b8b7cb
2025-08-17 10:37:16.793 free_tv_player[53159:8378677] [ProxyServer] 添加 Range 头部: bytes=0-1
2025-08-17 10:37:16.793 free_tv_player[53159:8378677] [ProxyServer] 代理请求到: https://video-play-c-zb-cf.pds.quark.cn/1TWfEooY/992844097/7e06c61718ab4bb8bebf32b54a2dd19767f8a91b/67f8a91b0a345891aa9b4da6889387fd82626e21?Expires=1755409692&OSSAccessKeyId=LTAI5tJJpWQEfrcKHnd1LqsZ&...
2025-08-17 10:37:16.793 free_tv_player[53159:8378677] [ProxyServer] 完整URL长度: 1479
2025-08-17 10:37:16.793 free_tv_player[53159:8378677] [ProxyServer] 请求头数量: 6
2025-08-17 10:37:16.793 free_tv_player[53159:8378677] [ProxyServer] 头部 Range: bytes=0-1
2025-08-17 10:37:16.793 free_tv_player[53159:8378677] [ProxyServer] 头部 Accept: */*
2025-08-17 10:37:16.793 free_tv_player[53159:8378677] [ProxyServer] 完整User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/2.5.20 Chrome/100.0.4896.160 Electron/********-b478491100 Safari/537.36 Channel/pckk_other_ch
2025-08-17 10:37:16.793 free_tv_player[53159:8378677] [ProxyServer] 头部 Accept-Encoding: gzip, deflate, br
2025-08-17 10:37:16.793 free_tv_player[53159:8378677] [ProxyServer] 头部 Accept-Language: zh-CN,zh;q=0.9
2025-08-17 10:37:16.793 free_tv_player[53159:8378677] [ProxyServer] 头部 Referer: https://pan.quark.cn/
2025-08-17 10:37:17.107 free_tv_player[53159:8378677] [ProxyServer] 代理响应: 403, 数据大小: 391
2025-08-17 10:37:17.107 free_tv_player[53159:8378677] [ProxyServer] 错误响应内容: <?xml version="1.0" encoding="UTF-8"?>
<Error>
<Code>RequestDeniedByCallback</Code>
<Message>Callback deny this request reason: require login [auth not found]</Message>
<RequestId>68A1405D7CC78C3539180CE3</RequestId>
<HostId>video-play-c-zb-cf.pds.quark.cn</HostId>
<EC>0007-00000209</EC>
<RecommendDoc>https://api.aliyun.com/troubleshoot?q=0007-00000209</RecommendDoc>
</Error>
2025-08-17 10:37:17.108 free_tv_player[53159:8376756] [ProxyServer] 收到请求: GET /proxy?key=0247df5bf039c112d341b8b509b8b7cb
2025-08-17 10:37:17.108 free_tv_player[53159:8376756] [ProxyServer] 添加 Range 头部: bytes=0-1
2025-08-17 10:37:17.108 free_tv_player[53159:8376756] [ProxyServer] 代理请求到: https://video-play-c-zb-cf.pds.quark.cn/1TWfEooY/992844097/7e06c61718ab4bb8bebf32b54a2dd19767f8a91b/67f8a91b0a345891aa9b4da6889387fd82626e21?Expires=1755409692&OSSAccessKeyId=LTAI5tJJpWQEfrcKHnd1LqsZ&...
2025-08-17 10:37:17.108 free_tv_player[53159:8376756] [ProxyServer] 完整URL长度: 1479
2025-08-17 10:37:17.108 free_tv_player[53159:8376756] [ProxyServer] 请求头数量: 6
2025-08-17 10:37:17.108 free_tv_player[53159:8376756] [ProxyServer] 完整User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/2.5.20 Chrome/100.0.4896.160 Electron/********-b478491100 Safari/537.36 Channel/pckk_other_ch
2025-08-17 10:37:17.108 free_tv_player[53159:8376756] [ProxyServer] 头部 Accept-Encoding: gzip, deflate, br
2025-08-17 10:37:17.108 free_tv_player[53159:8376756] [ProxyServer] 头部 Accept-Language: zh-CN,zh;q=0.9
2025-08-17 10:37:17.108 free_tv_player[53159:8376756] [ProxyServer] 头部 Referer: https://pan.quark.cn/
2025-08-17 10:37:17.108 free_tv_player[53159:8376756] [ProxyServer] 头部 Range: bytes=0-1
2025-08-17 10:37:17.108 free_tv_player[53159:8376756] [ProxyServer] 头部 Accept: */*
2025-08-17 10:37:17.207 free_tv_player[53159:8379688] [ProxyServer] 代理响应: 403, 数据大小: 391
2025-08-17 10:37:17.208 free_tv_player[53159:8379688] [ProxyServer] 错误响应内容: <?xml version="1.0" encoding="UTF-8"?>
<Error>
<Code>RequestDeniedByCallback</Code>
<Message>Callback deny this request reason: require login [auth not found]</Message>
<RequestId>68A1405D9CC26F3336895CF8</RequestId>
<HostId>video-play-c-zb-cf.pds.quark.cn</HostId>
<EC>0007-00000209</EC>
<RecommendDoc>https://api.aliyun.com/troubleshoot?q=0007-00000209</RecommendDoc>
</Error>

