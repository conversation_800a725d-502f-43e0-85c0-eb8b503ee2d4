#!/bin/bash

# JAR包解密脚本
# 基于AndroidCatVodSpider-multiThread项目的解密方法

JAR_FILE="$1"
OUTPUT_DIR="decrypted_output"

if [ -z "$JAR_FILE" ]; then
    echo "使用方法: $0 <jar文件路径>"
    echo "例如: $0 wogg_spider.jar"
    exit 1
fi

if [ ! -f "$JAR_FILE" ]; then
    echo "错误: JAR文件不存在: $JAR_FILE"
    exit 1
fi

echo "🔓 开始解密JAR包: $JAR_FILE"
echo "📁 输出目录: $OUTPUT_DIR"

# 清理之前的输出
rm -rf "$OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR"

# 第一步：解压JAR包
echo "📦 第一步: 解压JAR包..."
cd "$OUTPUT_DIR"
unzip -q "../$JAR_FILE"
echo "✅ JAR包解压完成"

# 第二步：查找DEX文件
echo "🔍 第二步: 查找DEX文件..."
DEX_FILES=$(find . -name "*.dex" -type f)
if [ -z "$DEX_FILES" ]; then
    echo "❌ 未找到DEX文件，这可能不是一个Android JAR包"
    exit 1
fi

echo "找到DEX文件:"
echo "$DEX_FILES"

# 第三步：反编译DEX文件
echo "🛠️  第三步: 反编译DEX文件..."
for dex_file in $DEX_FILES; do
    echo "正在反编译: $dex_file"
    java -jar "../baksmali-2.5.2.jar" d "$dex_file" -o "smali_$(basename $dex_file .dex)"
done

echo "✅ DEX文件反编译完成"

# 第四步：查找Spider相关代码
echo "🕷️  第四步: 查找Spider相关代码..."
SPIDER_FILES=$(find . -path "*/com/github/catvod/spider/*.smali" -type f 2>/dev/null)

if [ -n "$SPIDER_FILES" ]; then
    echo "🎉 找到Spider文件:"
    echo "$SPIDER_FILES" | head -10
    
    # 创建Spider代码目录
    mkdir -p spider_code
    find . -path "*/com/github/catvod/spider" -type d -exec cp -r {} spider_code/ \; 2>/dev/null
    
    echo "📋 Spider文件列表:"
    find spider_code -name "*.smali" -type f | sed 's/.*\///' | sort
else
    echo "⚠️  未找到标准的Spider代码结构"
    echo "📋 所有Smali文件:"
    find . -name "*.smali" -type f | head -20
fi

# 第五步：分析关键信息
echo "📊 第五步: 分析关键信息..."

# 查找网站URL
echo "🌐 查找网站URL..."
grep -r "https\?://[^\"']*" . --include="*.smali" | head -10

# 查找关键方法
echo "🔧 查找关键方法..."
grep -r "homeContent\|categoryContent\|detailContent\|searchContent\|playerContent" . --include="*.smali" | head -10

echo ""
echo "🎊 解密完成！"
echo "📁 解密结果保存在: $OUTPUT_DIR"
echo "🕷️  Spider代码位于: $OUTPUT_DIR/spider_code"
echo ""
echo "📖 下一步建议:"
echo "1. 查看 $OUTPUT_DIR/spider_code 目录中的Smali代码"
echo "2. 分析关键方法的实现逻辑"
echo "3. 将Java逻辑转换为Dart实现"

cd ..
