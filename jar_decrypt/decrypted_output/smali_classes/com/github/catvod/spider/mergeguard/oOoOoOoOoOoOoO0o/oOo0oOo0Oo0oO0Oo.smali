.class public final synthetic Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOo0oOo0Oo0oO0Oo;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

.field public final synthetic oOoOoOoOoOoOoO0o:I


# direct methods
.method public synthetic constructor <init>(Lcom/github/catvod/debug/MainActivity;I)V
    .registers 3

    .line 1
    iput p2, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOo0oOo0Oo0oO0Oo;->oOoOoOoOoOoOoO0o:I

    iput-object p1, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOo0oOo0Oo0oO0Oo;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .registers 3

    .line 1
    iget v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOo0oOo0Oo0oO0Oo;->oOoOoOoOoOoOoO0o:I

    packed-switch v0, :pswitch_data_2a

    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOo0oOo0Oo0oO0Oo;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-static {v0, p1}, Lcom/github/catvod/debug/MainActivity;->oOo0oOo0Oo0oO0Oo(Lcom/github/catvod/debug/MainActivity;Landroid/view/View;)V

    return-void

    :pswitch_b
    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOo0oOo0Oo0oO0Oo;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-static {v0, p1}, Lcom/github/catvod/debug/MainActivity;->oOoOoOo0oOo0o0oO(Lcom/github/catvod/debug/MainActivity;Landroid/view/View;)V

    return-void

    :pswitch_11
    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOo0oOo0Oo0oO0Oo;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-static {v0, p1}, Lcom/github/catvod/debug/MainActivity;->oOoOoOoOoOoOoO0o(Lcom/github/catvod/debug/MainActivity;Landroid/view/View;)V

    return-void

    :pswitch_17
    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOo0oOo0Oo0oO0Oo;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-static {v0, p1}, Lcom/github/catvod/debug/MainActivity;->oOoOo0O0Oo0o0OoO(Lcom/github/catvod/debug/MainActivity;Landroid/view/View;)V

    return-void

    :pswitch_1d
    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOo0oOo0Oo0oO0Oo;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-static {v0, p1}, Lcom/github/catvod/debug/MainActivity;->oOoOoOo0O0O0oO0o(Lcom/github/catvod/debug/MainActivity;Landroid/view/View;)V

    return-void

    :pswitch_23
    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOo0oOo0Oo0oO0Oo;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-static {v0, p1}, Lcom/github/catvod/debug/MainActivity;->oOoO0o0oOo0oO0Oo(Lcom/github/catvod/debug/MainActivity;Landroid/view/View;)V

    return-void

    nop

    :pswitch_data_2a
    .packed-switch 0x0
        :pswitch_23
        :pswitch_1d
        :pswitch_17
        :pswitch_11
        :pswitch_b
    .end packed-switch
.end method
