# FreeTV 技术规范补充文档

## 1. 数据解析器详细实现

### 1.1 XML数据源解析器
```dart
/// XML数据源解析器 - 对应TVBox的XML格式
class XmlDataSourceParser {
  /// 解析视频分类列表
  static List<VideoCategory> parseCategories(String xmlData) {
    final document = XmlDocument.parse(xmlData);
    final categories = <VideoCategory>[];
    
    // 查找分类节点
    final classNodes = document.findAllElements('class');
    for (final node in classNodes) {
      final id = node.getAttribute('id') ?? '';
      final name = node.innerText.trim();
      
      if (id.isNotEmpty && name.isNotEmpty) {
        categories.add(VideoCategory(
          id: id,
          name: name,
        ));
      }
    }
    
    return categories;
  }
  
  /// 解析视频列表
  static List<VideoInfo> parseVideoList(String xmlData) {
    final document = XmlDocument.parse(xmlData);
    final videos = <VideoInfo>[];
    
    // 查找视频节点
    final videoNodes = document.findAllElements('video');
    for (final node in videoNodes) {
      final video = _parseVideoNode(node);
      if (video != null) {
        videos.add(video);
      }
    }
    
    return videos;
  }
  
  /// 解析单个视频节点
  static VideoInfo? _parseVideoNode(XmlElement node) {
    try {
      final id = node.findElements('id').first.innerText;
      final name = _extractCDATA(node.findElements('name').first.innerText);
      final pic = node.findElements('pic').first.innerText;
      final actor = _extractCDATA(node.findElements('actor').first.innerText);
      final director = _extractCDATA(node.findElements('director').first.innerText);
      
      // 解析播放列表
      final episodeMap = <String, List<VideoEpisode>>{};
      final dlNodes = node.findAllElements('dl');
      
      for (int i = 0; i < dlNodes.length; i++) {
        final dlNode = dlNodes.elementAt(i);
        final flag = dlNode.getAttribute('flag') ?? '播放列表${i + 1}';
        final episodes = _parseEpisodes(dlNode.innerText);
        
        if (episodes.isNotEmpty) {
          episodeMap[flag] = episodes;
        }
      }
      
      return VideoInfo(
        id: id,
        name: name,
        pic: pic.isEmpty ? null : pic,
        actor: actor.isEmpty ? null : actor,
        director: director.isEmpty ? null : director,
        episodeMap: episodeMap,
      );
    } catch (e) {
      print('解析视频节点失败: $e');
      return null;
    }
  }
  
  /// 提取CDATA内容
  static String _extractCDATA(String text) {
    if (text.startsWith('<![CDATA[') && text.endsWith(']]>')) {
      return text.substring(9, text.length - 3);
    }
    return text;
  }
  
  /// 解析剧集列表
  static List<VideoEpisode> _parseEpisodes(String episodeText) {
    final episodes = <VideoEpisode>[];
    final parts = episodeText.split('#');
    
    for (int i = 0; i < parts.length; i++) {
      final part = parts[i].trim();
      if (part.isEmpty) continue;
      
      final dollarIndex = part.lastIndexOf('\$');
      if (dollarIndex > 0) {
        final name = part.substring(0, dollarIndex);
        final url = part.substring(dollarIndex + 1);
        
        episodes.add(VideoEpisode(
          name: name,
          url: url,
          index: i,
        ));
      }
    }
    
    return episodes;
  }
}
```

### 1.2 JSON数据源解析器
```dart
/// JSON数据源解析器
class JsonDataSourceParser {
  /// 解析视频分类列表
  static List<VideoCategory> parseCategories(String jsonData) {
    try {
      final data = jsonDecode(jsonData) as Map<String, dynamic>;
      final categories = <VideoCategory>[];
      
      // 解析分类数据
      final classList = data['class'] as List<dynamic>?;
      if (classList != null) {
        for (final item in classList) {
          if (item is Map<String, dynamic>) {
            categories.add(VideoCategory(
              id: item['type_id']?.toString() ?? '',
              name: item['type_name']?.toString() ?? '',
            ));
          }
        }
      }
      
      return categories;
    } catch (e) {
      throw DataParseException('JSON分类解析失败: $e');
    }
  }
  
  /// 解析视频列表
  static List<VideoInfo> parseVideoList(String jsonData) {
    try {
      final data = jsonDecode(jsonData) as Map<String, dynamic>;
      final videos = <VideoInfo>[];
      
      final videoList = data['list'] as List<dynamic>?;
      if (videoList != null) {
        for (final item in videoList) {
          if (item is Map<String, dynamic>) {
            final video = _parseVideoItem(item);
            if (video != null) {
              videos.add(video);
            }
          }
        }
      }
      
      return videos;
    } catch (e) {
      throw DataParseException('JSON视频列表解析失败: $e');
    }
  }
  
  /// 解析单个视频项
  static VideoInfo? _parseVideoItem(Map<String, dynamic> item) {
    try {
      final id = item['vod_id']?.toString() ?? '';
      final name = item['vod_name']?.toString() ?? '';
      final pic = item['vod_pic']?.toString() ?? '';
      final actor = item['vod_actor']?.toString() ?? '';
      final director = item['vod_director']?.toString() ?? '';
      
      // 解析播放列表
      final episodeMap = <String, List<VideoEpisode>>{};
      final playUrl = item['vod_play_url']?.toString() ?? '';
      final playFrom = item['vod_play_from']?.toString() ?? '';
      
      if (playUrl.isNotEmpty && playFrom.isNotEmpty) {
        final fromList = playFrom.split('\$\$\$');
        final urlList = playUrl.split('\$\$\$');
        
        for (int i = 0; i < fromList.length && i < urlList.length; i++) {
          final flag = fromList[i];
          final episodes = _parseJsonEpisodes(urlList[i]);
          
          if (episodes.isNotEmpty) {
            episodeMap[flag] = episodes;
          }
        }
      }
      
      return VideoInfo(
        id: id,
        name: name,
        pic: pic.isEmpty ? null : pic,
        actor: actor.isEmpty ? null : actor,
        director: director.isEmpty ? null : director,
        episodeMap: episodeMap,
      );
    } catch (e) {
      print('解析视频项失败: $e');
      return null;
    }
  }
  
  /// 解析JSON格式的剧集列表
  static List<VideoEpisode> _parseJsonEpisodes(String episodeText) {
    final episodes = <VideoEpisode>[];
    final parts = episodeText.split('#');
    
    for (int i = 0; i < parts.length; i++) {
      final part = parts[i].trim();
      if (part.isEmpty) continue;
      
      final dollarIndex = part.lastIndexOf('\$');
      if (dollarIndex > 0) {
        final name = part.substring(0, dollarIndex);
        final url = part.substring(dollarIndex + 1);
        
        episodes.add(VideoEpisode(
          name: name,
          url: url,
          index: i,
        ));
      }
    }
    
    return episodes;
  }
}

/// 数据解析异常
class DataParseException implements Exception {
  final String message;
  const DataParseException(this.message);
  
  @override
  String toString() => 'DataParseException: $message';
}
```

## 2. 数据库设计

### 2.1 Hive数据模型
```dart
/// 数据源存储模型
@HiveType(typeId: 0)
class DataSourceModel extends HiveObject {
  @HiveField(0)
  String key;
  
  @HiveField(1)
  String name;
  
  @HiveField(2)
  String api;
  
  @HiveField(3)
  int type; // 0: xml, 1: json, 3: spider
  
  @HiveField(4)
  bool searchable;
  
  @HiveField(5)
  bool quickSearch;
  
  @HiveField(6)
  String? ext;
  
  @HiveField(7)
  String? jar;
  
  @HiveField(8)
  int playerType;
  
  @HiveField(9)
  DateTime createdAt;
  
  @HiveField(10)
  DateTime updatedAt;
  
  DataSourceModel({
    required this.key,
    required this.name,
    required this.api,
    required this.type,
    this.searchable = false,
    this.quickSearch = false,
    this.ext,
    this.jar,
    this.playerType = 0,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();
  
  /// 转换为业务模型
  DataSource toDataSource() {
    return DataSource(
      key: key,
      name: name,
      api: api,
      type: DataSourceType.values[type],
      searchable: searchable,
      quickSearch: quickSearch,
      ext: ext,
      jar: jar,
      playerType: PlayerType.values[playerType],
    );
  }
  
  /// 从业务模型创建
  factory DataSourceModel.fromDataSource(DataSource dataSource) {
    return DataSourceModel(
      key: dataSource.key,
      name: dataSource.name,
      api: dataSource.api,
      type: dataSource.type.index,
      searchable: dataSource.searchable,
      quickSearch: dataSource.quickSearch,
      ext: dataSource.ext,
      jar: dataSource.jar,
      playerType: dataSource.playerType.index,
    );
  }
}

/// 播放历史记录模型
@HiveType(typeId: 1)
class PlayHistoryModel extends HiveObject {
  @HiveField(0)
  String videoId;
  
  @HiveField(1)
  String videoName;
  
  @HiveField(2)
  String? videoPic;
  
  @HiveField(3)
  String dataSourceKey;
  
  @HiveField(4)
  String episodeName;
  
  @HiveField(5)
  String episodeUrl;
  
  @HiveField(6)
  int episodeIndex;
  
  @HiveField(7)
  int position; // 播放位置（秒）
  
  @HiveField(8)
  int duration; // 总时长（秒）
  
  @HiveField(9)
  DateTime lastPlayTime;
  
  PlayHistoryModel({
    required this.videoId,
    required this.videoName,
    this.videoPic,
    required this.dataSourceKey,
    required this.episodeName,
    required this.episodeUrl,
    required this.episodeIndex,
    this.position = 0,
    this.duration = 0,
    DateTime? lastPlayTime,
  }) : lastPlayTime = lastPlayTime ?? DateTime.now();
}

/// 收藏记录模型
@HiveType(typeId: 2)
class FavoriteModel extends HiveObject {
  @HiveField(0)
  String videoId;
  
  @HiveField(1)
  String videoName;
  
  @HiveField(2)
  String? videoPic;
  
  @HiveField(3)
  String dataSourceKey;
  
  @HiveField(4)
  String? actor;
  
  @HiveField(5)
  String? director;
  
  @HiveField(6)
  DateTime createdAt;
  
  FavoriteModel({
    required this.videoId,
    required this.videoName,
    this.videoPic,
    required this.dataSourceKey,
    this.actor,
    this.director,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();
}
```

### 2.2 数据库服务实现
```dart
/// 数据库服务
class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  late Box<DataSourceModel> _dataSourceBox;
  late Box<PlayHistoryModel> _historyBox;
  late Box<FavoriteModel> _favoriteBox;

  /// 初始化数据库
  Future<void> initialize() async {
    // 注册适配器
    Hive.registerAdapter(DataSourceModelAdapter());
    Hive.registerAdapter(PlayHistoryModelAdapter());
    Hive.registerAdapter(FavoriteModelAdapter());

    // 打开数据库盒子
    _dataSourceBox = await Hive.openBox<DataSourceModel>('dataSources');
    _historyBox = await Hive.openBox<PlayHistoryModel>('playHistory');
    _favoriteBox = await Hive.openBox<FavoriteModel>('favorites');
  }

  /// 数据源操作
  Future<void> saveDataSource(DataSource dataSource) async {
    final model = DataSourceModel.fromDataSource(dataSource);
    await _dataSourceBox.put(dataSource.key, model);
  }

  List<DataSource> getAllDataSources() {
    return _dataSourceBox.values
        .map((model) => model.toDataSource())
        .toList();
  }

  Future<void> deleteDataSource(String key) async {
    await _dataSourceBox.delete(key);
  }

  /// 播放历史操作
  Future<void> savePlayHistory(PlayHistoryModel history) async {
    final key = '${history.dataSourceKey}_${history.videoId}';
    await _historyBox.put(key, history);
  }

  List<PlayHistoryModel> getPlayHistory({int limit = 50}) {
    final histories = _historyBox.values.toList();
    histories.sort((a, b) => b.lastPlayTime.compareTo(a.lastPlayTime));
    return histories.take(limit).toList();
  }

  Future<void> clearPlayHistory() async {
    await _historyBox.clear();
  }

  /// 收藏操作
  Future<void> addToFavorites(FavoriteModel favorite) async {
    final key = '${favorite.dataSourceKey}_${favorite.videoId}';
    await _favoriteBox.put(key, favorite);
  }

  Future<void> removeFromFavorites(String dataSourceKey, String videoId) async {
    final key = '${dataSourceKey}_$videoId';
    await _favoriteBox.delete(key);
  }

  List<FavoriteModel> getFavorites() {
    final favorites = _favoriteBox.values.toList();
    favorites.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return favorites;
  }

  bool isFavorite(String dataSourceKey, String videoId) {
    final key = '${dataSourceKey}_$videoId';
    return _favoriteBox.containsKey(key);
  }
}
```

## 3. 状态管理详细实现

### 3.1 应用全局状态
```dart
/// 应用全局状态管理
class AppState extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  Locale _locale = const Locale('zh', 'CN');
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  ThemeMode get themeMode => _themeMode;
  Locale get locale => _locale;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  /// 切换主题
  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
    notifyListeners();
    _saveThemeMode(mode);
  }

  /// 切换语言
  void setLocale(Locale locale) {
    _locale = locale;
    notifyListeners();
    _saveLocale(locale);
  }

  /// 设置加载状态
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// 清除错误
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// 初始化状态
  Future<void> initialize() async {
    await _loadThemeMode();
    await _loadLocale();
  }

  // 私有方法：持久化存储
  Future<void> _saveThemeMode(ThemeMode mode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('theme_mode', mode.index);
  }

  Future<void> _loadThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final index = prefs.getInt('theme_mode') ?? ThemeMode.system.index;
    _themeMode = ThemeMode.values[index];
  }

  Future<void> _saveLocale(Locale locale) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('locale', locale.toString());
  }

  Future<void> _loadLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final localeString = prefs.getString('locale') ?? 'zh_CN';
    final parts = localeString.split('_');
    _locale = Locale(parts[0], parts.length > 1 ? parts[1] : null);
  }
}
```

### 3.2 数据源状态管理
```dart
/// 数据源状态管理
class DataSourceState extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final DataSourceApiService _apiService = DataSourceApiService();

  List<DataSource> _dataSources = [];
  DataSource? _currentDataSource;
  List<VideoCategory> _categories = [];
  List<VideoInfo> _videos = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<DataSource> get dataSources => _dataSources;
  DataSource? get currentDataSource => _currentDataSource;
  List<VideoCategory> get categories => _categories;
  List<VideoInfo> get videos => _videos;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  /// 初始化数据源
  Future<void> initialize() async {
    _setLoading(true);
    try {
      _dataSources = _databaseService.getAllDataSources();
      if (_dataSources.isNotEmpty) {
        _currentDataSource = _dataSources.first;
        await loadCategories();
      }
    } catch (e) {
      _setError('初始化数据源失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 添加数据源
  Future<void> addDataSource(DataSource dataSource) async {
    try {
      await _databaseService.saveDataSource(dataSource);
      _dataSources.add(dataSource);
      notifyListeners();
    } catch (e) {
      _setError('添加数据源失败: $e');
    }
  }

  /// 删除数据源
  Future<void> removeDataSource(String key) async {
    try {
      await _databaseService.deleteDataSource(key);
      _dataSources.removeWhere((ds) => ds.key == key);

      if (_currentDataSource?.key == key) {
        _currentDataSource = _dataSources.isNotEmpty ? _dataSources.first : null;
        await loadCategories();
      }

      notifyListeners();
    } catch (e) {
      _setError('删除数据源失败: $e');
    }
  }

  /// 切换当前数据源
  Future<void> setCurrentDataSource(DataSource dataSource) async {
    if (_currentDataSource?.key != dataSource.key) {
      _currentDataSource = dataSource;
      await loadCategories();
    }
  }

  /// 加载分类列表
  Future<void> loadCategories() async {
    if (_currentDataSource == null) return;

    _setLoading(true);
    try {
      _categories = await _apiService.getCategories(_currentDataSource!);
      _videos.clear(); // 清空视频列表
      notifyListeners();
    } catch (e) {
      _setError('加载分类失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 加载视频列表
  Future<void> loadVideos(String categoryId, {int page = 1, bool append = false}) async {
    if (_currentDataSource == null) return;

    if (!append) _setLoading(true);

    try {
      final newVideos = await _apiService.getVideoList(
        _currentDataSource!,
        categoryId,
        page: page,
      );

      if (append) {
        _videos.addAll(newVideos);
      } else {
        _videos = newVideos;
      }

      notifyListeners();
    } catch (e) {
      _setError('加载视频列表失败: $e');
    } finally {
      if (!append) _setLoading(false);
    }
  }

  /// 搜索视频
  Future<List<VideoInfo>> searchVideos(String keyword, {int page = 1}) async {
    if (_currentDataSource == null || !_currentDataSource!.searchable) {
      throw Exception('当前数据源不支持搜索');
    }

    try {
      return await _apiService.searchVideos(
        _currentDataSource!,
        keyword,
        page: page,
      );
    } catch (e) {
      _setError('搜索失败: $e');
      return [];
    }
  }

  /// 获取视频详情
  Future<VideoInfo?> getVideoDetail(String videoId) async {
    if (_currentDataSource == null) return null;

    try {
      return await _apiService.getVideoDetail(_currentDataSource!, videoId);
    } catch (e) {
      _setError('获取视频详情失败: $e');
      return null;
    }
  }

  // 私有方法
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
```

### 3.3 播放器状态管理
```dart
/// 播放器状态管理
class VideoPlayerState extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();

  VideoPlayer? _player;
  VideoInfo? _currentVideo;
  VideoEpisode? _currentEpisode;
  PlayerType _playerType = PlayerType.videoPlayer;
  PlayerState _state = PlayerState.idle;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  double _volume = 1.0;
  double _playbackSpeed = 1.0;
  bool _isFullScreen = false;

  // Getters
  VideoInfo? get currentVideo => _currentVideo;
  VideoEpisode? get currentEpisode => _currentEpisode;
  PlayerType get playerType => _playerType;
  PlayerState get state => _state;
  Duration get position => _position;
  Duration get duration => _duration;
  double get volume => _volume;
  double get playbackSpeed => _playbackSpeed;
  bool get isFullScreen => _isFullScreen;
  bool get isPlaying => _state == PlayerState.playing;
  bool get isBuffering => _state == PlayerState.buffering;

  /// 初始化播放器
  Future<void> initializePlayer(PlayerType type) async {
    await _disposeCurrentPlayer();

    _playerType = type;

    switch (type) {
      case PlayerType.videoPlayer:
        _player = FlutterVideoPlayerImpl();
        break;
      case PlayerType.betterPlayer:
        _player = BetterPlayerImpl();
        break;
      default:
        _player = FlutterVideoPlayerImpl();
    }

    // 监听播放器状态
    _player!.stateStream.listen((state) {
      _state = state;
      notifyListeners();
    });

    _player!.positionStream.listen((position) {
      _position = position;
      notifyListeners();
    });
  }

  /// 播放视频
  Future<void> playVideo(VideoInfo video, VideoEpisode episode) async {
    try {
      _currentVideo = video;
      _currentEpisode = episode;

      if (_player == null) {
        await initializePlayer(_playerType);
      }

      await _player!.initialize(episode.url);
      await _player!.play();

      _duration = _player!.duration;

      // 保存播放历史
      await _savePlayHistory();

      notifyListeners();
    } catch (e) {
      _state = PlayerState.error;
      notifyListeners();
      throw PlayerException('播放失败: $e');
    }
  }

  /// 播放控制
  Future<void> play() async {
    await _player?.play();
  }

  Future<void> pause() async {
    await _player?.pause();
  }

  Future<void> seekTo(Duration position) async {
    await _player?.seekTo(position);
  }

  Future<void> setVolume(double volume) async {
    _volume = volume.clamp(0.0, 1.0);
    await _player?.setVolume(_volume);
    notifyListeners();
  }

  Future<void> setPlaybackSpeed(double speed) async {
    _playbackSpeed = speed;
    await _player?.setPlaybackSpeed(speed);
    notifyListeners();
  }

  /// 切换全屏
  void toggleFullScreen() {
    _isFullScreen = !_isFullScreen;
    notifyListeners();
  }

  /// 切换播放器类型
  Future<void> switchPlayer(PlayerType type) async {
    if (_playerType == type) return;

    final currentPosition = _position;
    final wasPlaying = isPlaying;

    await initializePlayer(type);

    if (_currentVideo != null && _currentEpisode != null) {
      await playVideo(_currentVideo!, _currentEpisode!);

      if (currentPosition > Duration.zero) {
        await seekTo(currentPosition);
      }

      if (!wasPlaying) {
        await pause();
      }
    }
  }

  /// 播放下一集
  Future<void> playNext() async {
    if (_currentVideo == null || _currentEpisode == null) return;

    final episodes = _getCurrentEpisodeList();
    if (episodes == null) return;

    final currentIndex = episodes.indexOf(_currentEpisode!);
    if (currentIndex >= 0 && currentIndex < episodes.length - 1) {
      final nextEpisode = episodes[currentIndex + 1];
      await playVideo(_currentVideo!, nextEpisode);
    }
  }

  /// 播放上一集
  Future<void> playPrevious() async {
    if (_currentVideo == null || _currentEpisode == null) return;

    final episodes = _getCurrentEpisodeList();
    if (episodes == null) return;

    final currentIndex = episodes.indexOf(_currentEpisode!);
    if (currentIndex > 0) {
      final previousEpisode = episodes[currentIndex - 1];
      await playVideo(_currentVideo!, previousEpisode);
    }
  }

  /// 获取当前剧集列表
  List<VideoEpisode>? _getCurrentEpisodeList() {
    if (_currentVideo == null || _currentEpisode == null) return null;

    for (final episodes in _currentVideo!.episodeMap.values) {
      if (episodes.contains(_currentEpisode)) {
        return episodes;
      }
    }

    return null;
  }

  /// 保存播放历史
  Future<void> _savePlayHistory() async {
    if (_currentVideo == null || _currentEpisode == null) return;

    final history = PlayHistoryModel(
      videoId: _currentVideo!.id,
      videoName: _currentVideo!.name,
      videoPic: _currentVideo!.pic,
      dataSourceKey: 'current', // 需要从上下文获取
      episodeName: _currentEpisode!.name,
      episodeUrl: _currentEpisode!.url,
      episodeIndex: _currentEpisode!.index,
      position: _position.inSeconds,
      duration: _duration.inSeconds,
    );

    await _databaseService.savePlayHistory(history);
  }

  /// 释放播放器资源
  Future<void> _disposeCurrentPlayer() async {
    await _player?.dispose();
    _player = null;
    _state = PlayerState.idle;
    _position = Duration.zero;
    _duration = Duration.zero;
  }

  @override
  void dispose() {
    _disposeCurrentPlayer();
    super.dispose();
  }
}
```

## 4. 核心UI组件实现

### 4.1 视频网格组件
```dart
/// 视频网格显示组件
class VideoGridView extends StatelessWidget {
  final List<VideoInfo> videos;
  final VoidCallback? onLoadMore;
  final bool isLoading;

  const VideoGridView({
    super.key,
    required this.videos,
    this.onLoadMore,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveLayoutManager.getGridColumns(context);

    return CustomScrollView(
      slivers: [
        SliverPadding(
          padding: const EdgeInsets.all(16),
          sliver: SliverStaggeredGrid.countBuilder(
            crossAxisCount: columns,
            itemCount: videos.length + (isLoading ? 1 : 0),
            itemBuilder: (context, index) {
              if (index >= videos.length) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              return VideoCard(
                video: videos[index],
                onTap: () => _onVideoTap(context, videos[index]),
              );
            },
            staggeredTileBuilder: (index) => const StaggeredTile.fit(1),
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
          ),
        ),
      ],
    );
  }

  void _onVideoTap(BuildContext context, VideoInfo video) {
    Navigator.pushNamed(
      context,
      AppRoutes.detail,
      arguments: video,
    );
  }
}

/// 视频卡片组件
class VideoCard extends StatelessWidget {
  final VideoInfo video;
  final VoidCallback? onTap;

  const VideoCard({
    super.key,
    required this.video,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 封面图片
            AspectRatio(
              aspectRatio: 3 / 4,
              child: CachedNetworkImage(
                imageUrl: video.pic ?? '',
                fit: BoxFit.cover,
                placeholder: (context, url) => Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    color: Colors.white,
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[200],
                  child: const Icon(
                    Icons.movie,
                    size: 48,
                    color: Colors.grey,
                  ),
                ),
              ),
            ),

            // 视频信息
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    video.name,
                    style: Theme.of(context).textTheme.titleSmall,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  if (video.actor != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      '主演: ${video.actor}',
                      style: Theme.of(context).textTheme.bodySmall,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],

                  if (video.year != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      '${video.year}年',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```
