# FreeTV Flutter 设计文档

## 1. 项目概述

### 1.1 项目定义
FreeTV是基于Flutter框架开发的跨平台影视播放器应用，灵感来源于TVBox项目。它支持多种视频源和播放协议，提供统一的用户体验，可运行在Android、iOS、Web、Windows、macOS和Linux平台上。

### 1.2 目标平台
- **移动端**: Android 5.0+, iOS 12.0+
- **桌面端**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **Web端**: Chrome 88+, Firefox 85+, Safari 14+

### 1.3 核心特性
- 多数据源支持 (XML/JSON/Spider)
- 多播放器内核集成
- 跨平台统一UI/UX
- 脚本引擎支持 (JavaScript)
- 实时搜索和收藏功能
- 字幕和弹幕支持
- 自定义主题系统

## 2. 技术架构

### 2.1 整体架构设计
```
┌─────────────────────────────────────────────────────────┐
│                    Presentation Layer                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │   Widgets   │ │   Screens   │ │    Controllers      │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                     Business Layer                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │  Services   │ │ Repositories│ │     Use Cases       │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                      Data Layer                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │ Data Sources│ │   Models    │ │     Database        │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                   Platform Layer                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │   Plugins   │ │   Native    │ │   Platform APIs     │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 2.2 状态管理架构
采用 **Provider + ChangeNotifier** 模式：
- **AppState**: 全局应用状态
- **VideoPlayerState**: 播放器状态管理
- **DataSourceState**: 数据源状态管理
- **ThemeState**: 主题状态管理
- **SearchState**: 搜索状态管理

### 2.3 Widget层次结构
```
MaterialApp
├── HomeScreen
│   ├── NavigationRail/BottomNavigationBar
│   ├── VideoGridView
│   └── SearchBar
├── DetailScreen
│   ├── VideoInfoWidget
│   ├── EpisodeListWidget
│   └── ActionButtonsWidget
├── PlayerScreen
│   ├── VideoPlayerWidget
│   ├── ControlsOverlay
│   └── SubtitleWidget
└── SettingsScreen
    ├── DataSourceSettings
    ├── PlayerSettings
    └── ThemeSettings
```

## 3. 核心模块设计

### 3.1 数据源管理模块 (DataSourceManager)
```dart
/// 数据源管理器 - 负责配置加载和数据源管理
class DataSourceManager {
  // 配置加载
  Future<void> loadConfig(String configUrl);
  
  // 数据源类型支持
  List<DataSource> getDataSources();
  
  // 加密配置解析
  String decryptConfig(String encryptedConfig);
  
  // 缓存管理
  void cacheConfig(String config);
}

/// 数据源类型枚举
enum DataSourceType {
  xml,      // XML格式数据源 (type=0)
  json,     // JSON格式数据源 (type=1)  
  spider,   // Spider爬虫数据源 (type=3)
}
```

### 3.2 视频播放器模块 (VideoPlayerManager)
```dart
/// 视频播放器管理器
class VideoPlayerManager {
  // 播放器类型
  PlayerType currentPlayerType;
  
  // 播放控制
  Future<void> play(String videoUrl);
  Future<void> pause();
  Future<void> seekTo(Duration position);
  
  // 播放器切换
  void switchPlayer(PlayerType type);
  
  // 字幕支持
  void loadSubtitle(String subtitleUrl);
}

/// 播放器类型枚举
enum PlayerType {
  system,    // 系统播放器
  videoPlayer, // video_player插件
  chewie,    // chewie播放器
  betterPlayer, // better_player插件
}
```

### 3.3 网络请求模块 (NetworkManager)
```dart
/// 网络请求管理器
class NetworkManager {
  late Dio _dio;
  
  // HTTP客户端配置
  void configureClient();
  
  // 请求方法
  Future<Response> get(String url, {Map<String, dynamic>? headers});
  Future<Response> post(String url, {dynamic data});
  
  // 代理支持
  void setProxy(String proxyUrl);
  
  // SSL配置
  void configureSsl();
}
```

## 4. 数据模型设计

### 4.1 数据源模型 (SourceBean -> DataSource)
```dart
/// 数据源模型 - 对应TVBox的SourceBean
class DataSource {
  final String key;           // 数据源标识
  final String name;          // 显示名称
  final String api;           // API地址
  final DataSourceType type;  // 数据源类型
  final bool searchable;      // 是否支持搜索
  final bool quickSearch;     // 是否支持快速搜索
  final String? ext;          // 扩展配置
  final String? jar;          // 自定义JAR包
  final PlayerType playerType; // 播放器类型
  
  const DataSource({
    required this.key,
    required this.name,
    required this.api,
    required this.type,
    this.searchable = false,
    this.quickSearch = false,
    this.ext,
    this.jar,
    this.playerType = PlayerType.system,
  });
  
  /// 从JSON创建实例
  factory DataSource.fromJson(Map<String, dynamic> json) {
    return DataSource(
      key: json['key'] ?? '',
      name: json['name'] ?? '',
      api: json['api'] ?? '',
      type: DataSourceType.values[json['type'] ?? 0],
      searchable: (json['searchable'] ?? 0) == 1,
      quickSearch: (json['quickSearch'] ?? 0) == 1,
      ext: json['ext'],
      jar: json['jar'],
      playerType: PlayerType.values[json['playerType'] ?? 0],
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'name': name,
      'api': api,
      'type': type.index,
      'searchable': searchable ? 1 : 0,
      'quickSearch': quickSearch ? 1 : 0,
      'ext': ext,
      'jar': jar,
      'playerType': playerType.index,
    };
  }
}
```

### 4.2 视频信息模型 (VodInfo -> VideoInfo)
```dart
/// 视频信息模型 - 对应TVBox的VodInfo
class VideoInfo {
  final String id;                    // 视频ID
  final String name;                  // 视频名称
  final String? pic;                  // 封面图片
  final String? actor;                // 演员
  final String? director;             // 导演
  final String? area;                 // 地区
  final int? year;                    // 年份
  final String? type;                 // 类型
  final String? note;                 // 描述
  final Map<String, List<VideoEpisode>> episodeMap; // 播放列表
  
  const VideoInfo({
    required this.id,
    required this.name,
    this.pic,
    this.actor,
    this.director,
    this.area,
    this.year,
    this.type,
    this.note,
    this.episodeMap = const {},
  });
  
  /// 从JSON创建实例
  factory VideoInfo.fromJson(Map<String, dynamic> json) {
    // 解析播放列表
    Map<String, List<VideoEpisode>> episodes = {};
    if (json['seriesMap'] != null) {
      Map<String, dynamic> seriesMap = json['seriesMap'];
      seriesMap.forEach((key, value) {
        if (value is List) {
          episodes[key] = value
              .map((e) => VideoEpisode.fromJson(e))
              .toList();
        }
      });
    }
    
    return VideoInfo(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      pic: json['pic'],
      actor: json['actor'],
      director: json['director'],
      area: json['area'],
      year: json['year'],
      type: json['type'],
      note: json['note'],
      episodeMap: episodes,
    );
  }
}

/// 视频剧集模型
class VideoEpisode {
  final String name;        // 剧集名称
  final String url;         // 播放地址
  final int index;          // 剧集索引
  
  const VideoEpisode({
    required this.name,
    required this.url,
    required this.index,
  });
  
  factory VideoEpisode.fromJson(Map<String, dynamic> json) {
    return VideoEpisode(
      name: json['name'] ?? '',
      url: json['url'] ?? '',
      index: json['index'] ?? 0,
    );
  }
}
```

## 5. 网络层设计

### 5.1 HTTP客户端实现
```dart
/// HTTP客户端服务 - 基于Dio实现
class HttpService {
  static final HttpService _instance = HttpService._internal();
  factory HttpService() => _instance;
  HttpService._internal();

  late Dio _dio;

  /// 初始化HTTP客户端
  void initialize() {
    _dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 10),
    ));

    // 添加拦截器
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
    ));

    // 添加重试拦截器
    _dio.interceptors.add(RetryInterceptor(
      dio: _dio,
      options: const RetryOptions(
        retries: 3,
        retryInterval: Duration(seconds: 1),
      ),
    ));
  }

  /// GET请求
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw NetworkException.fromDioError(e);
    }
  }

  /// POST请求
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw NetworkException.fromDioError(e);
    }
  }
}

/// 网络异常处理
class NetworkException implements Exception {
  final String message;
  final int? statusCode;

  const NetworkException(this.message, [this.statusCode]);

  factory NetworkException.fromDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return const NetworkException('连接超时');
      case DioExceptionType.receiveTimeout:
        return const NetworkException('接收超时');
      case DioExceptionType.badResponse:
        return NetworkException(
          '服务器错误: ${error.response?.statusCode}',
          error.response?.statusCode,
        );
      default:
        return const NetworkException('网络请求失败');
    }
  }
}
```

### 5.2 数据源API服务
```dart
/// 数据源API服务
class DataSourceApiService {
  final HttpService _httpService = HttpService();

  /// 获取视频分类列表
  Future<List<VideoCategory>> getCategories(DataSource dataSource) async {
    final response = await _httpService.get(dataSource.api);

    switch (dataSource.type) {
      case DataSourceType.xml:
        return _parseXmlCategories(response.data);
      case DataSourceType.json:
        return _parseJsonCategories(response.data);
      case DataSourceType.spider:
        return _parseSpiderCategories(response.data);
    }
  }

  /// 获取视频列表
  Future<List<VideoInfo>> getVideoList(
    DataSource dataSource,
    String categoryId, {
    int page = 1,
  }) async {
    final url = _buildVideoListUrl(dataSource, categoryId, page);
    final response = await _httpService.get(url);

    switch (dataSource.type) {
      case DataSourceType.xml:
        return _parseXmlVideoList(response.data);
      case DataSourceType.json:
        return _parseJsonVideoList(response.data);
      case DataSourceType.spider:
        return _parseSpiderVideoList(response.data);
    }
  }

  /// 搜索视频
  Future<List<VideoInfo>> searchVideos(
    DataSource dataSource,
    String keyword, {
    int page = 1,
  }) async {
    if (!dataSource.searchable) {
      throw Exception('该数据源不支持搜索');
    }

    final url = _buildSearchUrl(dataSource, keyword, page);
    final response = await _httpService.get(url);

    switch (dataSource.type) {
      case DataSourceType.xml:
        return _parseXmlVideoList(response.data);
      case DataSourceType.json:
        return _parseJsonVideoList(response.data);
      case DataSourceType.spider:
        return _parseSpiderVideoList(response.data);
    }
  }

  /// 获取视频详情
  Future<VideoInfo> getVideoDetail(
    DataSource dataSource,
    String videoId,
  ) async {
    final url = _buildDetailUrl(dataSource, videoId);
    final response = await _httpService.get(url);

    switch (dataSource.type) {
      case DataSourceType.xml:
        return _parseXmlVideoDetail(response.data);
      case DataSourceType.json:
        return _parseJsonVideoDetail(response.data);
      case DataSourceType.spider:
        return _parseSpiderVideoDetail(response.data);
    }
  }
}

/// 视频分类模型
class VideoCategory {
  final String id;
  final String name;
  final String? description;

  const VideoCategory({
    required this.id,
    required this.name,
    this.description,
  });

  factory VideoCategory.fromJson(Map<String, dynamic> json) {
    return VideoCategory(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
    );
  }
}
```

## 6. 视频播放器集成

### 6.1 播放器抽象接口
```dart
/// 视频播放器抽象接口
abstract class VideoPlayer {
  /// 播放器状态流
  Stream<PlayerState> get stateStream;

  /// 播放进度流
  Stream<Duration> get positionStream;

  /// 播放器控制方法
  Future<void> initialize(String videoUrl);
  Future<void> play();
  Future<void> pause();
  Future<void> seekTo(Duration position);
  Future<void> setVolume(double volume);
  Future<void> setPlaybackSpeed(double speed);
  Future<void> dispose();

  /// 播放器信息
  Duration get duration;
  Duration get position;
  bool get isPlaying;
  bool get isBuffering;
}

/// 播放器状态枚举
enum PlayerState {
  idle,
  loading,
  ready,
  playing,
  paused,
  buffering,
  completed,
  error,
}
```

### 6.2 多播放器实现
```dart
/// Flutter Video Player 实现
class FlutterVideoPlayerImpl implements VideoPlayer {
  late VideoPlayerController _controller;
  final StreamController<PlayerState> _stateController = StreamController.broadcast();
  final StreamController<Duration> _positionController = StreamController.broadcast();

  @override
  Stream<PlayerState> get stateStream => _stateController.stream;

  @override
  Stream<Duration> get positionStream => _positionController.stream;

  @override
  Future<void> initialize(String videoUrl) async {
    try {
      _stateController.add(PlayerState.loading);

      _controller = VideoPlayerController.networkUrl(Uri.parse(videoUrl));
      await _controller.initialize();

      // 监听播放状态变化
      _controller.addListener(_onPlayerStateChanged);

      _stateController.add(PlayerState.ready);
    } catch (e) {
      _stateController.add(PlayerState.error);
      throw PlayerException('播放器初始化失败: $e');
    }
  }

  @override
  Future<void> play() async {
    await _controller.play();
    _stateController.add(PlayerState.playing);
  }

  @override
  Future<void> pause() async {
    await _controller.pause();
    _stateController.add(PlayerState.paused);
  }

  @override
  Future<void> seekTo(Duration position) async {
    await _controller.seekTo(position);
  }

  @override
  Future<void> setVolume(double volume) async {
    await _controller.setVolume(volume);
  }

  @override
  Future<void> setPlaybackSpeed(double speed) async {
    await _controller.setPlaybackSpeed(speed);
  }

  @override
  Future<void> dispose() async {
    await _controller.dispose();
    await _stateController.close();
    await _positionController.close();
  }

  @override
  Duration get duration => _controller.value.duration;

  @override
  Duration get position => _controller.value.position;

  @override
  bool get isPlaying => _controller.value.isPlaying;

  @override
  bool get isBuffering => _controller.value.isBuffering;

  void _onPlayerStateChanged() {
    _positionController.add(_controller.value.position);

    if (_controller.value.isBuffering) {
      _stateController.add(PlayerState.buffering);
    } else if (_controller.value.isPlaying) {
      _stateController.add(PlayerState.playing);
    } else if (_controller.value.position >= _controller.value.duration) {
      _stateController.add(PlayerState.completed);
    }
  }
}

/// Better Player 实现
class BetterPlayerImpl implements VideoPlayer {
  late BetterPlayerController _controller;
  final StreamController<PlayerState> _stateController = StreamController.broadcast();
  final StreamController<Duration> _positionController = StreamController.broadcast();

  @override
  Stream<PlayerState> get stateStream => _stateController.stream;

  @override
  Stream<Duration> get positionStream => _positionController.stream;

  @override
  Future<void> initialize(String videoUrl) async {
    try {
      _stateController.add(PlayerState.loading);

      final betterPlayerDataSource = BetterPlayerDataSource(
        BetterPlayerDataSourceType.network,
        videoUrl,
        bufferingConfiguration: const BetterPlayerBufferingConfiguration(
          minBufferMs: 2000,
          maxBufferMs: 10000,
          bufferForPlaybackMs: 1000,
          bufferForPlaybackAfterRebufferMs: 2000,
        ),
      );

      _controller = BetterPlayerController(
        const BetterPlayerConfiguration(
          aspectRatio: 16 / 9,
          autoPlay: false,
          looping: false,
          fullScreenByDefault: false,
        ),
        betterPlayerDataSource: betterPlayerDataSource,
      );

      // 监听播放状态
      _controller.addEventsListener(_onBetterPlayerEvent);

      _stateController.add(PlayerState.ready);
    } catch (e) {
      _stateController.add(PlayerState.error);
      throw PlayerException('Better Player初始化失败: $e');
    }
  }

  @override
  Future<void> play() async {
    await _controller.play();
  }

  @override
  Future<void> pause() async {
    await _controller.pause();
  }

  @override
  Future<void> seekTo(Duration position) async {
    await _controller.seekTo(position);
  }

  @override
  Future<void> setVolume(double volume) async {
    await _controller.setVolume(volume);
  }

  @override
  Future<void> setPlaybackSpeed(double speed) async {
    await _controller.setSpeed(speed);
  }

  @override
  Future<void> dispose() async {
    _controller.dispose();
    await _stateController.close();
    await _positionController.close();
  }

  @override
  Duration get duration => _controller.videoPlayerController?.value.duration ?? Duration.zero;

  @override
  Duration get position => _controller.videoPlayerController?.value.position ?? Duration.zero;

  @override
  bool get isPlaying => _controller.isPlaying() ?? false;

  @override
  bool get isBuffering => _controller.videoPlayerController?.value.isBuffering ?? false;

  void _onBetterPlayerEvent(BetterPlayerEvent event) {
    switch (event.betterPlayerEventType) {
      case BetterPlayerEventType.play:
        _stateController.add(PlayerState.playing);
        break;
      case BetterPlayerEventType.pause:
        _stateController.add(PlayerState.paused);
        break;
      case BetterPlayerEventType.bufferingStart:
        _stateController.add(PlayerState.buffering);
        break;
      case BetterPlayerEventType.bufferingEnd:
        _stateController.add(PlayerState.playing);
        break;
      case BetterPlayerEventType.finished:
        _stateController.add(PlayerState.completed);
        break;
      default:
        break;
    }

    _positionController.add(position);
  }
}

/// 播放器异常类
class PlayerException implements Exception {
  final String message;
  const PlayerException(this.message);

  @override
  String toString() => 'PlayerException: $message';
}
```

## 7. 平台适配设计

### 7.1 响应式UI设计
```dart
/// 响应式布局管理器
class ResponsiveLayoutManager {
  /// 获取当前设备类型
  static DeviceType getDeviceType(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return DeviceType.mobile;
    } else if (screenWidth < 1200) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  /// 获取适配的列数
  static int getGridColumns(BuildContext context) {
    final deviceType = getDeviceType(context);

    switch (deviceType) {
      case DeviceType.mobile:
        return 2;
      case DeviceType.tablet:
        return 3;
      case DeviceType.desktop:
        return 4;
    }
  }

  /// 获取适配的导航类型
  static NavigationType getNavigationType(BuildContext context) {
    final deviceType = getDeviceType(context);

    switch (deviceType) {
      case DeviceType.mobile:
        return NavigationType.bottomNavigation;
      case DeviceType.tablet:
      case DeviceType.desktop:
        return NavigationType.navigationRail;
    }
  }
}

/// 设备类型枚举
enum DeviceType {
  mobile,
  tablet,
  desktop,
}

/// 导航类型枚举
enum NavigationType {
  bottomNavigation,
  navigationRail,
}
```

### 7.2 平台特定功能
```dart
/// 平台适配服务
class PlatformAdaptationService {
  /// 检查当前平台
  static bool get isMobile => Platform.isAndroid || Platform.isIOS;
  static bool get isDesktop => Platform.isWindows || Platform.isMacOS || Platform.isLinux;
  static bool get isWeb => kIsWeb;

  /// 获取平台特定的播放器
  static PlayerType getRecommendedPlayer() {
    if (Platform.isAndroid) {
      return PlayerType.betterPlayer; // Android推荐Better Player
    } else if (Platform.isIOS) {
      return PlayerType.videoPlayer; // iOS推荐Video Player
    } else if (isDesktop) {
      return PlayerType.videoPlayer; // 桌面端推荐Video Player
    } else if (isWeb) {
      return PlayerType.videoPlayer; // Web端推荐Video Player
    }
    return PlayerType.system;
  }

  /// 获取平台特定的存储路径
  static Future<String> getStoragePath() async {
    if (isMobile) {
      final directory = await getApplicationDocumentsDirectory();
      return directory.path;
    } else if (isDesktop) {
      final directory = await getApplicationSupportDirectory();
      return directory.path;
    } else {
      return './storage'; // Web端使用相对路径
    }
  }

  /// 平台特定的文件选择
  static Future<String?> pickConfigFile() async {
    if (isWeb) {
      // Web端使用file_picker
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json', 'txt'],
      );
      return result?.files.single.name;
    } else {
      // 移动端和桌面端
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json', 'txt'],
      );
      return result?.files.single.path;
    }
  }

  /// 平台特定的网络配置
  static void configurePlatformNetwork() {
    if (Platform.isAndroid) {
      // Android特定网络配置
      HttpOverrides.global = AndroidHttpOverrides();
    } else if (Platform.isIOS) {
      // iOS特定网络配置
      HttpOverrides.global = IOSHttpOverrides();
    }
  }
}

/// Android HTTP覆写
class AndroidHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  }
}

/// iOS HTTP覆写
class IOSHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  }
}
```

## 8. 依赖管理

### 8.1 核心依赖包
```yaml
# pubspec.yaml
dependencies:
  flutter:
    sdk: flutter

  # 状态管理
  provider: ^6.1.1

  # 网络请求
  dio: ^5.4.0
  dio_retry: ^4.0.0

  # 视频播放器
  video_player: ^2.8.1
  chewie: ^1.7.4
  better_player: ^0.0.83

  # 数据解析
  xml: ^6.4.2
  html: ^0.15.4

  # 数据库
  sqflite: ^2.3.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # 文件系统
  path_provider: ^2.1.1
  file_picker: ^6.1.1

  # UI组件
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  flutter_staggered_grid_view: ^0.7.0

  # 工具类
  crypto: ^3.0.3
  convert: ^3.1.1
  intl: ^0.19.0

  # 平台特定
  url_launcher: ^6.2.1
  share_plus: ^7.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  hive_generator: ^2.0.1
  build_runner: ^2.4.7
```

### 8.2 JavaScript引擎集成
```dart
/// JavaScript引擎服务 - 用于Spider数据源解析
class JavaScriptEngineService {
  late JavaScriptRuntime _runtime;

  /// 初始化JS引擎
  Future<void> initialize() async {
    _runtime = getJavaScriptRuntime();
  }

  /// 执行JavaScript代码
  Future<String> executeScript(String script, Map<String, dynamic> context) async {
    try {
      // 注入上下文变量
      for (final entry in context.entries) {
        _runtime.evaluate('var ${entry.key} = ${jsonEncode(entry.value)};');
      }

      // 执行脚本
      final result = _runtime.evaluate(script);
      return result.stringValue;
    } catch (e) {
      throw JavaScriptException('JavaScript执行失败: $e');
    }
  }

  /// 执行Spider解析脚本
  Future<Map<String, dynamic>> executeSpiderScript(
    String script,
    String url,
    String html,
  ) async {
    final context = {
      'url': url,
      'html': html,
      'request': _createRequestFunction(),
    };

    final result = await executeScript(script, context);
    return jsonDecode(result);
  }

  /// 创建请求函数
  Map<String, dynamic> _createRequestFunction() {
    return {
      'get': (String url) async {
        final response = await HttpService().get(url);
        return response.data;
      },
      'post': (String url, Map<String, dynamic> data) async {
        final response = await HttpService().post(url, data: data);
        return response.data;
      },
    };
  }

  /// 释放资源
  void dispose() {
    _runtime.dispose();
  }
}

/// JavaScript异常类
class JavaScriptException implements Exception {
  final String message;
  const JavaScriptException(this.message);

  @override
  String toString() => 'JavaScriptException: $message';
}
```

## 9. 项目文件结构

### 9.1 推荐的Flutter项目组织
```
free_tv/
├── lib/
│   ├── main.dart                    # 应用入口
│   ├── app/
│   │   ├── app.dart                 # 应用配置
│   │   ├── routes.dart              # 路由配置
│   │   └── themes.dart              # 主题配置
│   ├── core/
│   │   ├── constants/               # 常量定义
│   │   ├── errors/                  # 错误处理
│   │   ├── network/                 # 网络层
│   │   ├── platform/                # 平台适配
│   │   └── utils/                   # 工具类
│   ├── data/
│   │   ├── datasources/             # 数据源
│   │   │   ├── local/               # 本地数据源
│   │   │   └── remote/              # 远程数据源
│   │   ├── models/                  # 数据模型
│   │   └── repositories/            # 数据仓库
│   ├── domain/
│   │   ├── entities/                # 业务实体
│   │   ├── repositories/            # 仓库接口
│   │   └── usecases/                # 用例
│   ├── presentation/
│   │   ├── providers/               # 状态管理
│   │   ├── screens/                 # 页面
│   │   │   ├── home/                # 首页
│   │   │   ├── detail/              # 详情页
│   │   │   ├── player/              # 播放器页
│   │   │   ├── search/              # 搜索页
│   │   │   └── settings/            # 设置页
│   │   └── widgets/                 # 通用组件
│   │       ├── common/              # 通用组件
│   │       ├── video/               # 视频相关组件
│   │       └── player/              # 播放器组件
│   └── services/
│       ├── database/                # 数据库服务
│       ├── player/                  # 播放器服务
│       ├── javascript/              # JS引擎服务
│       └── storage/                 # 存储服务
├── assets/
│   ├── images/                      # 图片资源
│   ├── icons/                       # 图标资源
│   └── fonts/                       # 字体资源
├── test/                            # 单元测试
├── integration_test/                # 集成测试
├── android/                         # Android平台代码
├── ios/                             # iOS平台代码
├── web/                             # Web平台代码
├── windows/                         # Windows平台代码
├── macos/                           # macOS平台代码
├── linux/                           # Linux平台代码
└── pubspec.yaml                     # 依赖配置
```

### 9.2 核心文件示例

#### main.dart
```dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'app/app.dart';
import 'core/platform/platform_adaptation_service.dart';
import 'core/network/http_service.dart';
import 'services/database/database_service.dart';
import 'presentation/providers/app_state.dart';
import 'presentation/providers/video_player_state.dart';
import 'presentation/providers/data_source_state.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化Hive数据库
  await Hive.initFlutter();

  // 平台适配配置
  PlatformAdaptationService.configurePlatformNetwork();

  // 初始化服务
  HttpService().initialize();
  await DatabaseService().initialize();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AppState()),
        ChangeNotifierProvider(create: (_) => VideoPlayerState()),
        ChangeNotifierProvider(create: (_) => DataSourceState()),
      ],
      child: const FreeTVApp(),
    ),
  );
}
```

#### app/app.dart
```dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'routes.dart';
import 'themes.dart';
import '../presentation/providers/app_state.dart';

class FreeTVApp extends StatelessWidget {
  const FreeTVApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return MaterialApp(
          title: 'FreeTV',
          theme: AppThemes.lightTheme,
          darkTheme: AppThemes.darkTheme,
          themeMode: appState.themeMode,
          initialRoute: AppRoutes.home,
          routes: AppRoutes.routes,
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}
```

## 10. 实现路线图

### 10.1 第一阶段：基础架构 (2-3周)
**目标**: 建立项目基础架构和核心功能

**任务清单**:
- [ ] 创建Flutter项目并配置多平台支持
- [ ] 实现基础的状态管理架构 (Provider)
- [ ] 搭建网络层 (Dio + 拦截器)
- [ ] 实现数据模型 (DataSource, VideoInfo等)
- [ ] 创建基础UI框架 (导航、主题)
- [ ] 实现本地数据库 (Hive/SQLite)

**交付物**:
- 可运行的基础应用框架
- 基本的导航和页面结构
- 网络请求和数据存储功能

### 10.2 第二阶段：数据源集成 (3-4周)
**目标**: 实现多种数据源的解析和管理

**任务清单**:
- [ ] 实现XML数据源解析器
- [ ] 实现JSON数据源解析器
- [ ] 集成JavaScript引擎 (flutter_js)
- [ ] 实现Spider数据源解析器
- [ ] 创建数据源管理界面
- [ ] 实现配置文件加载和缓存

**交付物**:
- 完整的数据源管理系统
- 支持XML/JSON/Spider三种数据源
- 配置文件导入导出功能

### 10.3 第三阶段：视频播放器 (2-3周)
**目标**: 集成多种播放器并实现播放功能

**任务清单**:
- [ ] 集成video_player插件
- [ ] 集成better_player插件
- [ ] 实现播放器抽象接口
- [ ] 创建播放器控制界面
- [ ] 实现播放历史记录
- [ ] 添加字幕支持

**交付物**:
- 功能完整的视频播放器
- 支持多种播放器内核切换
- 播放控制和进度管理

### 10.4 第四阶段：用户界面优化 (2-3周)
**目标**: 完善用户界面和用户体验

**任务清单**:
- [ ] 实现响应式布局设计
- [ ] 优化视频列表和详情页面
- [ ] 实现搜索功能界面
- [ ] 添加收藏和历史功能
- [ ] 实现设置页面
- [ ] 添加主题切换功能

**交付物**:
- 完整的用户界面
- 响应式设计支持
- 用户个性化功能

### 10.5 第五阶段：平台适配 (3-4周)
**目标**: 适配不同平台并优化性能

**任务清单**:
- [ ] Android平台优化和测试
- [ ] iOS平台适配和测试
- [ ] Web平台适配
- [ ] 桌面端 (Windows/macOS/Linux) 适配
- [ ] 性能优化和内存管理
- [ ] 平台特定功能集成

**交付物**:
- 全平台支持的应用
- 平台特定功能集成
- 性能优化版本

### 10.6 第六阶段：高级功能 (2-3周)
**目标**: 实现高级功能和扩展特性

**任务清单**:
- [ ] 弹幕功能实现
- [ ] 投屏功能 (DLNA/Chromecast)
- [ ] 离线下载功能
- [ ] 多语言支持
- [ ] 数据同步功能
- [ ] 插件系统设计

**交付物**:
- 高级功能完整版本
- 扩展性良好的架构
- 用户体验优化

## 11. 技术风险和解决方案

### 11.1 主要技术风险
1. **跨平台兼容性**: 不同平台的播放器表现差异
2. **网络请求稳定性**: 数据源的可用性和稳定性
3. **JavaScript引擎性能**: Spider解析的执行效率
4. **内存管理**: 视频播放和大量数据的内存占用

### 11.2 解决方案
1. **播放器适配**: 为每个平台选择最优的播放器实现
2. **网络重试机制**: 实现智能重试和降级策略
3. **JS引擎优化**: 使用Web Worker或Isolate进行异步处理
4. **内存优化**: 实现图片缓存管理和视频资源释放

## 12. 总结

FreeTV Flutter版本的设计充分考虑了跨平台开发的特点，将TVBox的Android原生架构转换为Flutter的响应式架构。通过模块化设计、抽象接口和状态管理，实现了代码的高复用性和可维护性。

**核心优势**:
- **跨平台统一**: 一套代码支持6个平台
- **模块化架构**: 清晰的分层设计，易于扩展
- **多播放器支持**: 灵活的播放器切换机制
- **响应式设计**: 适配不同屏幕尺寸和设备类型
- **扩展性强**: 支持JavaScript脚本和插件系统

**技术亮点**:
- Provider状态管理确保数据流清晰
- Dio网络层提供强大的HTTP功能
- 抽象播放器接口支持多种播放器
- 平台适配服务处理平台差异
- JavaScript引擎支持Spider数据源

这个设计文档为FreeTV的Flutter实现提供了完整的技术蓝图，可以作为开发团队的实施指南。通过分阶段的实现路线图，可以确保项目的稳步推进和质量控制。
```
```
