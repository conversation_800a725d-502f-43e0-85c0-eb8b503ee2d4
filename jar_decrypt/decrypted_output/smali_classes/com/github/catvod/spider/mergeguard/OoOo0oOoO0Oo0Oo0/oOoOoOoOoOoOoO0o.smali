.class public abstract synthetic Lcom/github/catvod/spider/mergeguard/OoOo0oOoO0Oo0Oo0/oOoOoOoOoOoOoO0o;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public static bridge synthetic oOo0oOo0Oo0oO0Oo()Ljava/nio/charset/Charset;
    .registers 1

    .line 1
    sget-object v0, Ljava/nio/charset/StandardCharsets;->UTF_8:L<PERSON>va/nio/charset/Charset;

    return-object v0
.end method

.method public static bridge synthetic oOoOoOoOoOoOoO0o(Ljava/util/zip/Deflater;[BII)I
    .registers 5

    .line 1
    const/4 v0, 0x2

    invoke-virtual {p0, p1, p2, p3, v0}, Ljava/util/zip/Deflater;->deflate([BIII)I

    move-result p0

    return p0
.end method
