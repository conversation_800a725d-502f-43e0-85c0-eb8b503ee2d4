.class public final synthetic Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOoOoOoOoOoOoO0o;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

.field public final synthetic oOoOoOoOoOoOoO0o:I


# direct methods
.method public synthetic constructor <init>(Lcom/github/catvod/debug/MainActivity;I)V
    .registers 3

    .line 1
    iput p2, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOoOoOoOoOoOoO0o;->oOoOoOoOoOoOoO0o:I

    iput-object p1, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOoOoOoOoOoOoO0o;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final run()V
    .registers 2

    .line 1
    iget v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOoOoOoOoOoOoO0o;->oOoOoOoOoOoOoO0o:I

    packed-switch v0, :pswitch_data_30

    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOoOoOoOoOoOoO0o;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-virtual {v0}, Lcom/github/catvod/debug/MainActivity;->searchContent()V

    return-void

    :pswitch_b
    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOoOoOoOoOoOoO0o;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-virtual {v0}, Lcom/github/catvod/debug/MainActivity;->categoryContent()V

    return-void

    :pswitch_11
    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOoOoOoOoOoOoO0o;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-virtual {v0}, Lcom/github/catvod/debug/MainActivity;->detailContent()V

    return-void

    :pswitch_17
    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOoOoOoOoOoOoO0o;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-virtual {v0}, Lcom/github/catvod/debug/MainActivity;->homeContent()V

    return-void

    :pswitch_1d
    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOoOoOoOoOoOoO0o;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-virtual {v0}, Lcom/github/catvod/debug/MainActivity;->homeVideoContent()V

    return-void

    :pswitch_23
    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOoOoOoOoOoOoO0o;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-static {v0}, Lcom/github/catvod/debug/MainActivity;->OoOoOo0O0o0oO0o0(Lcom/github/catvod/debug/MainActivity;)V

    return-void

    :pswitch_29
    iget-object v0, p0, Lcom/github/catvod/spider/mergeguard/oOoOoOoOoOoOoO0o/oOoOoOoOoOoOoO0o;->oOo0oOo0Oo0oO0Oo:Lcom/github/catvod/debug/MainActivity;

    invoke-virtual {v0}, Lcom/github/catvod/debug/MainActivity;->playerContent()V

    return-void

    nop

    :pswitch_data_30
    .packed-switch 0x0
        :pswitch_29
        :pswitch_23
        :pswitch_1d
        :pswitch_17
        :pswitch_11
        :pswitch_b
    .end packed-switch
.end method
