import Cocoa
import FlutterMacOS
import Network
import Foundation
import CommonCrypto
import VLCKit

// HTTP 代理服务器类 - 仿照 AndroidCatVodSpider ProxyServer 实现
class ProxyServer: NSObject {
  static let shared = ProxyServer()
  
  private var listener: NWListener?
  private var urlMap: [String: String] = [:]
  private var headerMap: [String: [String: String]] = [:]
  private let port: UInt16 = 9999
  private let queue = DispatchQueue(label: "proxy-server", qos: .userInitiated)
  
  // 流式代理连接跟踪
  private var activeConnections: [String: NWConnection] = [:]
  
  // 增加M3U8内容缓存机制，避免重复处理
  private var m3u8Cache: [String: (content: String, timestamp: Date)] = [:]
  private let cacheTimeout: TimeInterval = 60 // 60秒缓存
  
  // 对同一个key的并发请求进行去重
  private var pendingRequests: [String: [NWConnection]] = [:]
  
  // 请求重试机制
  private let maxRetries = 3
  private var retryAttempts: [String: Int] = [:]
  
  private override init() {
    super.init()
  }
  
  func start() {
    guard listener == nil else { return }
    
    do {
      let parameters = NWParameters.tcp
      parameters.allowLocalEndpointReuse = true
      parameters.includePeerToPeer = true
      
      listener = try NWListener(using: parameters, on: NWEndpoint.Port(rawValue: port)!)
      
      listener?.newConnectionHandler = { [weak self] connection in
        self?.handleConnection(connection)
      }
      
      listener?.start(queue: queue)
      NSLog("[ProxyServer] 已启动代理服务器，端口: \(port)")
    } catch {
      NSLog("[ProxyServer] 启动失败: \(error)")
    }
  }

  // 发布已重写的 M3U8 内容：写入缓存并向等待同一 key 的连接立即回发
  func publishM3U8Content(originalUrl: String, content: String) {
    let key = originalUrl.md5
    let data = content.data(using: .utf8) ?? Data()
    // 在服务器队列上串行处理，避免竞争
    queue.async { [weak self] in
      guard let self = self else { return }
      self.m3u8Cache[key] = (content: content, timestamp: Date())
      if let waitingConnections = self.pendingRequests[key] {
        for conn in waitingConnections {
          self.sendCachedM3U8Response(conn, data: data)
        }
        self.pendingRequests.removeValue(forKey: key)
      }
    }
  }
  
  func stop() {
    listener?.cancel()
    listener = nil
    urlMap.removeAll()
    headerMap.removeAll()
    m3u8Cache.removeAll()
    pendingRequests.removeAll()
    retryAttempts.removeAll()
    NSLog("[ProxyServer] 代理服务器已停止")
  }
  
  func buildProxyUrl(_ url: String, headers: [String: String]) -> String {
    let key = url.md5
    urlMap[key] = url
    headerMap[key] = headers
    NSLog("[ProxyServer] 构建代理URL: key=\(key)")
    NSLog("[ProxyServer] 原始URL长度: \(url.count)")
    NSLog("[ProxyServer] 完整原始URL: \(url)")
//    if url.count <= 200 {
//      NSLog("[ProxyServer] 原始URL: \(url)")
//    } else {
//      NSLog("[ProxyServer] 原始URL: \(url.prefix(200))...")
//    }
    return "http://127.0.0.1:\(port)/proxy?key=\(key)"
  }
  
  private func handleConnection(_ connection: NWConnection) {
    connection.start(queue: queue)
    
    // 为连接添加状态监听，避免僵尸连接
    connection.stateUpdateHandler = { state in
      switch state {
      case .failed(let error):
        NSLog("[ProxyServer] 连接失败: \(error)")
      case .cancelled:
        NSLog("[ProxyServer] 连接已取消")
      default:
        break
      }
    }
    
    func receiveRequest() {
      connection.receive(minimumIncompleteLength: 1, maximumLength: 65536) { [weak self] data, _, isComplete, error in
        if let error = error {
          let nsError = error as NSError
          if nsError.code != 54 { // 只记录非"Connection reset by peer"的错误
            NSLog("[ProxyServer] 接收错误: \(error)")
          }
          connection.cancel()
          return
        }
        
        guard let data = data, !data.isEmpty else {
          if isComplete {
            connection.cancel()
          } else {
            receiveRequest()
          }
          return
        }
        
        if let request = String(data: data, encoding: .utf8) {
          self?.processRequest(request, connection: connection)
        } else {
          connection.cancel()
        }
      }
    }
    
    receiveRequest()
  }
  
  private func processRequest(_ request: String, connection: NWConnection) {
    let lines = request.components(separatedBy: "\r\n")
    guard let firstLine = lines.first else {
      sendErrorResponse(connection, status: 400, message: "Bad Request")
      return
    }
    
    let parts = firstLine.components(separatedBy: " ")
    guard parts.count >= 2 else {
      sendErrorResponse(connection, status: 400, message: "Bad Request")
      return
    }
    
    let method = parts[0]
    let urlPath = parts[1]
    
    // 解析 Range 头部
    var rangeHeader: String?
    var userAgent: String?
    for line in lines {
      if line.lowercased().hasPrefix("range:") {
        rangeHeader = String(line.dropFirst(6).trimmingCharacters(in: .whitespaces))
      } else if line.lowercased().hasPrefix("user-agent:") {
        userAgent = String(line.dropFirst(11).trimmingCharacters(in: .whitespaces))
      }
    }
    
    // 播放器兼容性检测
    let playerInfo = detectPlayerType(userAgent: userAgent, rangeHeader: rangeHeader)
    NSLog("[ProxyServer] 收到请求: \(method) \(urlPath), 播放器: \(playerInfo.name)")
    
    if urlPath.hasPrefix("/proxy?key=") {
      let keyParam = String(urlPath.dropFirst(11))
      handleProxyRequest(key: keyParam, method: method, rangeHeader: rangeHeader, connection: connection, playerInfo: playerInfo)
    } else {
      sendErrorResponse(connection, status: 404, message: "Not Found")
    }
  }
  
  // 播放器类型检测
  private func detectPlayerType(userAgent: String?, rangeHeader: String?) -> (name: String, isLowLatency: Bool, preferredChunkSize: Int) {
    let ua = userAgent?.lowercased() ?? ""
    
    // 检测不同播放器类型
    if ua.contains("vlc") {
      return ("VLC", false, 2 * 1024 * 1024) // VLC偏好2MB块
    } else if ua.contains("mpv") || ua.contains("iina") {
      return ("MPV/IINA", true, 1 * 1024 * 1024) // MPV系列偏好1MB块，低延迟
    } else if ua.contains("chrome") || ua.contains("safari") || ua.contains("webkit") {
      // 浏览器内置播放器
      if let range = rangeHeader, range == "bytes=0-1" {
        return ("浏览器探测", true, 512 * 1024) // 浏览器探测请求，小块传输
      } else {
        return ("浏览器播放器", false, 1 * 1024 * 1024) // 普通浏览器播放器
      }
    } else if ua.contains("flutter") || ua.contains("better_player") || ua.contains("video_player") {
      return ("Flutter播放器", true, 512 * 1024) // Flutter播放器偏好小块，低延迟
    } else if ua.contains("avfoundation") || ua.contains("coremedia") {
      return ("AVPlayer", true, 1 * 1024 * 1024) // macOS原生播放器
    } else {
      // 未知播放器，使用保守策略
      return ("未知播放器", false, 1 * 1024 * 1024)
    }
  }
  
  private func handleProxyRequest(key: String, method: String, rangeHeader: String?, connection: NWConnection, playerInfo: (name: String, isLowLatency: Bool, preferredChunkSize: Int)) {
    guard let originalUrl = urlMap[key],
          let headers = headerMap[key] else {
      NSLog("[ProxyServer] ❌ 未找到 key: \(key)")
      sendErrorResponse(connection, status: 404, message: "Key not found")
      return
    }
    
    // 检测文件类型并记录详细日志
    let isTS = originalUrl.lowercased().hasSuffix(".ts") || originalUrl.lowercased().contains(".ts?")
    let isM3U8 = originalUrl.lowercased().hasSuffix(".m3u8") || originalUrl.lowercased().contains(".m3u8?")
    let fileType = isM3U8 ? "M3U8" : (isTS ? "TS" : "其他")
    
    NSLog("[ProxyServer] 🎯 处理\(fileType)请求: key=\(key.prefix(8))..., 播放器=\(playerInfo.name)")
    if isTS {
      // 提取TS文件名用于调试
      if let tsFileName = originalUrl.components(separatedBy: "/").last?.components(separatedBy: "?").first {
        NSLog("[ProxyServer] 📹 TS片段: \(tsFileName)")
      }
    }
    
    // 检查是否是M3U8文件且有缓存
    if isM3U8 {
      // 检查缓存
      if let cached = m3u8Cache[key], 
         Date().timeIntervalSince(cached.timestamp) < cacheTimeout {
        NSLog("[ProxyServer] 使用M3U8缓存")
        let responseData = cached.content.data(using: .utf8) ?? Data()
        sendCachedM3U8Response(connection, data: responseData)
        return
      }
      
      // 检查是否有在处理的请求
      if pendingRequests[key] != nil {
        NSLog("[ProxyServer] M3U8请求正在处理，加入等待队列")
        pendingRequests[key]?.append(connection)
        return
      } else {
        pendingRequests[key] = [connection]
      }
    }
    
    guard let url = URL(string: originalUrl) else {
      sendErrorResponse(connection, status: 400, message: "Invalid URL")
      return
    }
    
    var request = URLRequest(url: url)
    request.httpMethod = method
    request.httpShouldHandleCookies = false
    request.timeoutInterval = 30
    
    // 设置所有头部
    var outboundHeaders = headers
    let lowercasedUrl = url.absoluteString.lowercased()
    let isTSFile = url.path.lowercased().hasSuffix(".ts") || lowercasedUrl.contains(".ts?")
    let isM3U8File = url.path.lowercased().hasSuffix(".m3u8") || lowercasedUrl.contains(".m3u8?")
    let isQuarkCDN = lowercasedUrl.contains("pds.quark.cn") || 
                     lowercasedUrl.contains("video-play") || 
                     lowercasedUrl.contains("video-h-kdac") ||
                     lowercasedUrl.contains("drive.quark.cn")
    
    // 调试日志：显示文件类型检测结果
    if isTSFile {
      NSLog("[ProxyServer] 🎬 检测到TS文件: \(url.path)")
    } else if isM3U8File {
      NSLog("[ProxyServer] 📋 检测到M3U8文件: \(url.path)")
    } else {
      NSLog("[ProxyServer] 📁 其他文件类型: \(url.path)")
    }
    
    if isQuarkCDN {
      NSLog("[ProxyServer] ✅ 夸克CDN确认")
    }
    
    // 移除可能导致412错误的条件性头部（对M3U8文件更严格）
    let conditionalHeaders = [
      "If-Modified-Since", "If-None-Match", "If-Match", "If-Unmodified-Since",
      "If-Range", "ETag", "Last-Modified", "Cache-Control", "Pragma"
    ]
    
    for header in conditionalHeaders {
      outboundHeaders.removeValue(forKey: header)
    }
    
    // 根据AndroidCatVodSpider的成功实现，对夸克CDN明确移除Host和Content-Type头部
    if isQuarkCDN && (isTS || isM3U8File) {
      outboundHeaders.removeValue(forKey: "Host")
      outboundHeaders.removeValue(forKey: "Content-Type")
      NSLog("[ProxyServer] 夸克CDN: 移除Host和Content-Type头部")
    }
    
    // 对M3U8文件进行额外的头部清理
    if isM3U8File {
      NSLog("[ProxyServer] M3U8文件检测到，进行特殊处理")
      // 移除所有可能的缓存相关头部
      let m3u8Headers = ["Accept-Encoding", "Accept-Ranges", "Connection"]
      for header in m3u8Headers {
        outboundHeaders.removeValue(forKey: header)
      }
      // 强制使用最简单的请求头
      outboundHeaders["Cache-Control"] = "no-cache, no-store, must-revalidate"
      outboundHeaders["Pragma"] = "no-cache"
    }
    
    // 夸克CDN特殊处理：需要保留Cookie进行认证
    if (isTS || isM3U8File) && !isQuarkCDN { 
      outboundHeaders.removeValue(forKey: "Cookie") 
    }
    
    // 对于夸克CDN的M3U8和TS文件，确保有Cookie认证
    if (isM3U8File || isTS) && isQuarkCDN {
      let fileType = isM3U8File ? "M3U8" : "TS"
      NSLog("[ProxyServer] 夸克\(fileType)文件，确保Cookie认证")
      if let savedCookie = UserDefaults.standard.string(forKey: "free_tv_quark_cookie"), !savedCookie.isEmpty {
        outboundHeaders["Cookie"] = savedCookie
        NSLog("[ProxyServer] 为\(fileType)添加Cookie认证，长度: \(savedCookie.count)")
      }
    }
    
    if isM3U8File {
      outboundHeaders["Accept-Encoding"] = "identity"
      outboundHeaders["Origin"] = "https://pan.quark.cn"  // 移除尾部斜杠
      outboundHeaders["Accept"] = "*/*"  // 简化Accept头部，避免过于特殊化
    } else {
      // 对于非 m3u8 媒体请求，禁用压缩避免 Range 与编码长度不一致
      outboundHeaders["Accept-Encoding"] = "identity"
    }
    
    // 夸克CDN增强认证
    if isQuarkCDN {
      NSLog("[ProxyServer] 检测到夸克CDN，增强认证头部")
      outboundHeaders["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/2.5.20 Chrome/100.0.4896.160 Electron/********-b478491100 Safari/537.36 Channel/pckk_other_ch"
      outboundHeaders["Referer"] = "https://pan.quark.cn/"
      if outboundHeaders["Origin"] == nil {
        outboundHeaders["Origin"] = "https://pan.quark.cn"  // 移除尾部斜杠
      }
      outboundHeaders["Accept-Encoding"] = "identity"
      
      // 添加关键的浏览器指纹头部，参考AndroidCatVodSpider成功实现
      outboundHeaders["Sec-Ch-Ua"] = "\"Chromium\";v=\"100\", \"Not(A:Brand\";v=\"8\", \"Google Chrome\";v=\"100\""
      outboundHeaders["Sec-Ch-Ua-Mobile"] = "?0"
      outboundHeaders["Sec-Ch-Ua-Platform"] = "\"Windows\""
      outboundHeaders["Sec-Fetch-Dest"] = isM3U8File ? "empty" : (isTSFile ? "video" : "empty")
      outboundHeaders["Sec-Fetch-Mode"] = "cors"
      outboundHeaders["Sec-Fetch-Site"] = "cross-site"
      
      // 为TS文件设置合适的Accept头部
      if isTSFile {
        outboundHeaders["Accept"] = "*/*"
        outboundHeaders["Accept-Language"] = "zh-CN,zh;q=0.9"
      }
      if let savedCookie = UserDefaults.standard.string(forKey: "free_tv_quark_cookie"), 
         !savedCookie.isEmpty {
        outboundHeaders["Cookie"] = savedCookie
        NSLog("[ProxyServer] 使用保存的Cookie: \(savedCookie.prefix(50))...")
      } else {
        NSLog("[ProxyServer] 警告：未找到有效的夸克Cookie")
      }
    }
    // 诊断性日志：完整打印 outboundHeaders（即将用于上游请求）
    NSLog("[ProxyServer] ▶︎ outboundHeaders 列表（用于上游请求）：")
    for (key, value) in outboundHeaders {
      if key.lowercased() == "cookie" {
        NSLog("[ProxyServer]   \(key): <cookie length=\(value.count)>")
      } else {
        NSLog("[ProxyServer]   \(key): \(value)")
      }
    }
    for (key, value) in outboundHeaders { request.setValue(value, forHTTPHeaderField: key) }
    
    // Range 透传：对M3U8和夸克TS文件特殊处理，避免412错误
    if let range = rangeHeader {
      if isM3U8File {
        NSLog("[ProxyServer] 跳过 m3u8 的 Range 头部: \(range)")
      } else if isTS && isQuarkCDN && range.hasPrefix("bytes=0-") {
        // 夸克TS文件的探测性Range请求可能导致412错误，跳过初始探测
        NSLog("[ProxyServer] 跳过夸克TS文件的探测性Range头部: \(range)")
      } else {
        request.setValue(range, forHTTPHeaderField: "Range")
        outboundHeaders["Range"] = range
        NSLog("[ProxyServer] 透传 Range 头部: \(range)")
      }
    }
    // 若客户端未带 Range，且为夸克 TS，则默认添加 bytes=0-，必要时在上游返回412时由流式处理器回退
    if rangeHeader == nil && isTS && isQuarkCDN {
      request.setValue("bytes=0-", forHTTPHeaderField: "Range")
      outboundHeaders["Range"] = "bytes=0-"
      NSLog("[ProxyServer] 夸克TS默认添加 Range: bytes=0-")
    }
    
    NSLog("[ProxyServer] 代理请求到: \(originalUrl.prefix(200))...")
    NSLog("[ProxyServer] 完整URL长度: \(originalUrl.count)")
    if originalUrl.contains("x-oss-process=") {
      NSLog("[ProxyServer] URL包含 x-oss-process 参数")
    }
    // URL 诊断性日志：空白字符与结构
    let hasWhitespace = originalUrl.rangeOfCharacter(from: .whitespacesAndNewlines) != nil
    if hasWhitespace {
      NSLog("[ProxyServer] 警告：URL 含有空白/换行字符")
      NSLog("[ProxyServer] URL尾部(最多20字符)：\(originalUrl.suffix(20))")
    }
    if let u = URL(string: originalUrl) {
      NSLog("[ProxyServer] URL Host: \(u.host ?? "<nil>") Path: \(u.path)")
    }
    
    // 打印关键请求头
    if let allHeaders = request.allHTTPHeaderFields {
      NSLog("[ProxyServer] 请求头数量: \(allHeaders.count)")
      for (key, value) in allHeaders {
        if key.lowercased() == "cookie" {
          NSLog("[ProxyServer] Cookie长度: \(value.count)")
        } else if key.lowercased() == "range" {
          NSLog("[ProxyServer] Range: \(value)")
        } else if key.lowercased() == "user-agent" {
          NSLog("[ProxyServer] User-Agent: \(value.prefix(50))...")
        }
      }
    }
    
    // 检查是否需要流式传输
    // M3U8文件本身是小文件，不应使用流式传输；流式传输应用于其包含的TS片段
    let needsStreaming = (isQuarkCDN || url.absoluteString.lowercased().contains(".mp4")) && !isM3U8File
    
    if needsStreaming {
      NSLog("[ProxyServer] 使用流式代理处理视频文件，播放器: \(playerInfo.name)")
      // 检查连接状态
      guard connection.state == .ready else {
        NSLog("[ProxyServer] 连接状态异常，跳过流式传输: \(connection.state)")
        sendErrorResponse(connection, status: 500, message: "Connection Not Ready")
        return
      }
      let streamingHandler = StreamingProxyHandler(
        originalUrl: originalUrl, 
        headers: outboundHeaders, 
        connection: connection,
        playerInfo: playerInfo
      )
      streamingHandler.startStreaming()
    } else {
      // 对于小文件，使用标准代理
      let config = URLSessionConfiguration.default
      
      // M3U8文件特殊配置
      if isM3U8File {
        NSLog("[ProxyServer] M3U8文件使用特殊配置")
        config.requestCachePolicy = .reloadIgnoringLocalAndRemoteCacheData
        config.urlCache = nil // 完全禁用缓存
        config.timeoutIntervalForRequest = 15 // 更短的超时
        config.timeoutIntervalForResource = 30
        config.httpMaximumConnectionsPerHost = 1 // 限制连接数
      } else {
        config.requestCachePolicy = .reloadIgnoringLocalCacheData
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 60
      }
      config.waitsForConnectivity = true // 等待网络连接
      
      let session = URLSession(configuration: config)
      
      let task = session.dataTask(with: request) { [weak self] data, response, error in
        defer {
          session.invalidateAndCancel()
        }
        
        if let error = error {
          let nsError = error as NSError
          let currentRetries = self?.retryAttempts[key] ?? 0
          
          // 检查是否应该重试
          if currentRetries < (self?.maxRetries ?? 3) && 
             (nsError.code == NSURLErrorTimedOut || 
              nsError.code == NSURLErrorNetworkConnectionLost ||
              nsError.code == NSURLErrorNotConnectedToInternet) {
            self?.retryAttempts[key] = currentRetries + 1
            NSLog("[ProxyServer] 小文件请求失败，重试 \(currentRetries + 1)/\(self?.maxRetries ?? 3): \(error)")
            
            // 延迟重试
            DispatchQueue.global().asyncAfter(deadline: .now() + 1.0) {
              // 创建默认播放器信息用于重试
              let defaultPlayerInfo = ("重试播放器", false, 1024 * 1024)
              self?.handleProxyRequest(key: key, method: method, rangeHeader: rangeHeader, connection: connection, playerInfo: defaultPlayerInfo)
            }
            return
          }
          
          // 检测文件类型并记录详细错误信息
          let isTSError = originalUrl.lowercased().hasSuffix(".ts") || originalUrl.lowercased().contains(".ts?")
          if isTSError {
            NSLog("[ProxyServer] ❌ TS片段请求失败（达到最大重试次数）: \(error)")
            if let tsFileName = originalUrl.components(separatedBy: "/").last?.components(separatedBy: "?").first {
              NSLog("[ProxyServer] 💥 失败的TS片段: \(tsFileName)")
            }
          } else {
            NSLog("[ProxyServer] 小文件请求失败（达到最大重试次数）: \(error)")
          }
          self?.sendErrorResponse(connection, status: 500, message: "Proxy Error: \(error.localizedDescription)")
          return
        }
        
        // 重置重试计数
        self?.retryAttempts.removeValue(forKey: key)
        
        guard let httpResponse = response as? HTTPURLResponse else {
          self?.sendErrorResponse(connection, status: 500, message: "Invalid Response")
          return
        }
        
        let responseSize = data?.count ?? 0
        NSLog("[ProxyServer] 小文件响应: \(httpResponse.statusCode), 数据大小: \(responseSize)")
        
        // 检测文件类型并记录详细信息
        let isTSSuccess = originalUrl.lowercased().hasSuffix(".ts") || originalUrl.lowercased().contains(".ts?")
        if isTSSuccess {
          let sizeInKB = responseSize / 1024
          NSLog("[ProxyServer] ✅ TS片段下载成功: \(sizeInKB)KB")
          if httpResponse.statusCode != 200 {
            NSLog("[ProxyServer] ⚠️ TS片段状态异常: \(httpResponse.statusCode)")
          }
        }
        
        self?.sendProxyResponse(connection, response: httpResponse, data: data, originalUrl: originalUrl, headers: headers)
      }
      
      task.resume()
      NSLog("[ProxyServer] 启动小文件代理任务，ID: \(task.taskIdentifier)")
    }
  }
  
  // 大文件流式响应处理
  private func handleStreamingResponse(connection: NWConnection, data: Data?, response: URLResponse?, error: Error?, originalUrl: String, headers: [String: String]) {
    if let error = error {
      NSLog("[ProxyServer] 大文件流式请求失败: \(error)")
      sendErrorResponse(connection, status: 500, message: "Streaming Error")
      return
    }
    
    guard let httpResponse = response as? HTTPURLResponse else {
      sendErrorResponse(connection, status: 500, message: "Invalid Streaming Response")
      return
    }
    
    NSLog("[ProxyServer] 大文件流式响应: \(httpResponse.statusCode), 数据大小: \(data?.count ?? 0)")
    
    // 对于大文件，立即开始发送响应头，然后流式传输数据
    sendProxyResponse(connection, response: httpResponse, data: data, originalUrl: originalUrl, headers: headers)
  }
  
  private func sendProxyResponse(_ connection: NWConnection, response: HTTPURLResponse, data: Data?, originalUrl: String, headers: [String: String]) {
    var responseData = data
    var responseHeaders = "HTTP/1.1 \(response.statusCode) \(HTTPURLResponse.localizedString(forStatusCode: response.statusCode))\r\n"
    
    let key = originalUrl.md5
    let isM3U8Check = originalUrl.hasSuffix(".m3u8") || originalUrl.contains(".m3u8?")
    
    // 如果是 M3U8 文件，需要修改内容将 TS URL 转换为代理 URL
    if let contentType = response.allHeaderFields["Content-Type"] as? String,
       (contentType.contains("application/vnd.apple.mpegurl") || contentType.contains("application/x-mpegURL") || isM3U8Check),
       let data = data,
       let m3u8Content = String(data: data, encoding: .utf8) {
      
      let modifiedContent = modifyM3U8Content(m3u8Content, baseUrl: originalUrl, headers: headers)
      responseData = modifiedContent.data(using: .utf8)
      
      // 缓存M3U8内容
      m3u8Cache[key] = (content: modifiedContent, timestamp: Date())
      
      NSLog("[ProxyServer] 修改了 M3U8 内容，原始行数: \(m3u8Content.components(separatedBy: "\n").count), 修改后行数: \(modifiedContent.components(separatedBy: "\n").count)")
      
      // 向所有等待的连接发送响应
      if let waitingConnections = pendingRequests[key] {
        for waitingConnection in waitingConnections {
          if let cachedData = responseData {
            sendCachedM3U8Response(waitingConnection, data: cachedData)
          }
        }
        pendingRequests.removeValue(forKey: key)
        return // 已经处理所有连接，直接返回
      }
    }
    
    // 复制重要的响应头部
    let importantHeaders = ["Content-Type", "Content-Range", "Accept-Ranges", "Cache-Control"]
    for header in importantHeaders {
      if let value = response.allHeaderFields[header] as? String {
        responseHeaders += "\(header): \(value)\r\n"
      }
    }
    
    // 更新 Content-Length（因为可能修改了内容）
    if let data = responseData {
      responseHeaders += "Content-Length: \(data.count)\r\n"
    }
    
    // CORS 头部
    responseHeaders += "Access-Control-Allow-Origin: *\r\n"
    responseHeaders += "Access-Control-Allow-Methods: GET, POST, OPTIONS\r\n"
    responseHeaders += "Access-Control-Allow-Headers: *\r\n"
    
    responseHeaders += "\r\n"
    
    var finalResponseData = responseHeaders.data(using: .utf8) ?? Data()
    if let data = responseData {
      finalResponseData.append(data)
    }
    
    connection.send(content: finalResponseData, completion: .contentProcessed { error in
      if let error = error {
        NSLog("[ProxyServer] 发送响应失败: \(error)")
        connection.cancel()
        return
      } else {
        NSLog("[ProxyServer] 代理响应发送成功: \(finalResponseData.count) 字节")
      }
      
      // M3U8 文件发送成功后，不能关闭连接，需要等待播放器请求TS片段
      if isM3U8Check {
        NSLog("[ProxyServer] M3U8 响应发送完成，保持连接等待 TS 请求。")
      } else {
        connection.cancel()
      }
    })
    
    NSLog("[ProxyServer] 代理响应: \(response.statusCode), 数据大小: \(responseData?.count ?? 0)")
    
    // 为大文件添加额外的状态监控
    if let data = responseData, data.count > 1000000 { // 超过1MB
      NSLog("[ProxyServer] ⚠️  大文件响应 (\(data.count/1024/1024)MB)，可能需要更多时间传输")
    }
    
    // 如果是错误响应，记录错误内容
    if response.statusCode >= 400, let errorData = responseData, let errorString = String(data: errorData, encoding: .utf8) {
      NSLog("[ProxyServer] 错误响应内容: \(errorString)")
    }
  }
  
  private func modifyM3U8Content(_ content: String, baseUrl: String, headers: [String: String]) -> String {
    // 使用通用换行符分割，避免遗留 \r 导致 URL 携带回车字符
    let lines = content.components(separatedBy: .newlines)
    var modifiedLines: [String] = []

    guard URL(string: baseUrl) != nil else {
      return content
    }

    // 获取基本URL用于相对路径拼接
    let baseUrlComponents = baseUrl.components(separatedBy: "/").dropLast()
    let baseUrlWithoutFile = baseUrlComponents.joined(separator: "/")

    for line in lines {
      // 去除行两端的空白和换行，避免 TS URL 含有不可见字符（如 \r）
      var trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)

      // 处理 KEY 与 MAP 标签中的 URI，确保也通过本地代理携带鉴权头访问
      if trimmedLine.hasPrefix("#EXT-X-KEY:") || trimmedLine.hasPrefix("#EXT-X-MAP:") {
        if let uriRange = trimmedLine.range(of: "URI=\"", options: .caseInsensitive) {
          let startIndex = uriRange.upperBound
          if let endQuoteRange = trimmedLine[startIndex...].firstIndex(of: "\"") {
            let originalUri = String(trimmedLine[startIndex..<endQuoteRange])
            let absoluteUri: String
            if originalUri.hasPrefix("http") {
              absoluteUri = originalUri
            } else {
              let baseUrlWithoutFile = baseUrl.components(separatedBy: "/").dropLast().joined(separator: "/")
              absoluteUri = "\(baseUrlWithoutFile)/\(originalUri)"
            }
            var keyHeaders = headers
            let isQuark = absoluteUri.lowercased().contains("quark.cn") || absoluteUri.lowercased().contains("video-h-kdac") || absoluteUri.lowercased().contains("drive.quark.cn")
            if !isQuark { keyHeaders.removeValue(forKey: "Cookie") } else { keyHeaders["Origin"] = "https://pan.quark.cn/" }
            let proxyUri = ProxyServer.shared.buildProxyUrl(absoluteUri, headers: keyHeaders)
            let prefix = String(trimmedLine[..<startIndex])
            let suffix = String(trimmedLine[endQuoteRange...])
            trimmedLine = prefix + proxyUri + suffix
            NSLog("[StreamingProxy] 重写标签URI: \(originalUri) -> \(proxyUri)")
          }
        }
        modifiedLines.append(trimmedLine)
        continue
      }

      // 处理 KEY 与 MAP 标签中的 URI，确保也通过本地代理携带鉴权头访问
      if trimmedLine.hasPrefix("#EXT-X-KEY:") || trimmedLine.hasPrefix("#EXT-X-MAP:") {
        // 寻找 URI="..." 参数并替换为代理 URL
        if let uriRange = trimmedLine.range(of: "URI=\"", options: .caseInsensitive) {
          let startIndex = uriRange.upperBound
          if let endQuoteRange = trimmedLine[startIndex...].firstIndex(of: "\"") {
            let originalUri = String(trimmedLine[startIndex..<endQuoteRange])
            // 拼接为完整 URL（相对路径时）
            let absoluteUri: String
            if originalUri.hasPrefix("http") {
              absoluteUri = originalUri
            } else {
              absoluteUri = "\(baseUrlWithoutFile)/\(originalUri)"
            }
            // KEY/MAP 访问需要与 TS 相同的鉴权头（Cookie/Origin等）
            var keyHeaders = headers
            let isQuark = absoluteUri.lowercased().contains("quark.cn") || absoluteUri.lowercased().contains("video-h-kdac") || absoluteUri.lowercased().contains("drive.quark.cn")
            if !isQuark { keyHeaders.removeValue(forKey: "Cookie") } else { keyHeaders["Origin"] = "https://pan.quark.cn/" }
            let proxyUri = buildProxyUrl(absoluteUri, headers: keyHeaders)
            // 用代理 URL 替换原 URI
            let prefix = String(trimmedLine[..<startIndex])
            let suffix = String(trimmedLine[endQuoteRange...])
            trimmedLine = prefix + proxyUri + suffix
            NSLog("[ProxyServer] 重写标签URI: \(originalUri) -> \(proxyUri)")
          }
        }
        modifiedLines.append(trimmedLine)
        continue
      }

      // 统一净化：删除第一个空白(Unicode)及其后的所有内容（涵盖全角/零宽等情况）
      let originalForLog = trimmedLine
      if !trimmedLine.hasPrefix("#") && !trimmedLine.isEmpty {
        NSLog("[ProxyServer] 🧹 正在进行TS行净化")
        let sanitized = trimmedLine.replacingOccurrences(of: "\\p{Z}+.*$", with: "", options: [.regularExpression])
        if sanitized != trimmedLine {
          NSLog("[ProxyServer] 🧹 TS行净化: 原='\(originalForLog)' 净='\(sanitized)'")
          trimmedLine = sanitized
        }
      }

      // 如果是 TS 文件行（不以 # 开头且包含 .ts）
      if !trimmedLine.hasPrefix("#") && !trimmedLine.isEmpty {
        if trimmedLine.hasSuffix(".ts") || trimmedLine.contains(".ts?") {
          // 构建完整的 TS URL
          let tsUrl: String
          if trimmedLine.hasPrefix("http") {
            // 已经是完整 URL
            tsUrl = trimmedLine
          } else {
            // 相对路径，需要拼接
            tsUrl = "\(baseUrlWithoutFile)/\(trimmedLine)"
          }

          // 为 TS URL 创建代理映射并生成代理 URL
          // 注意：TS 段需要保留 Cookie 用于夸克 CDN 认证
          var tsHeaders = headers
          let lowercasedTsUrl = tsUrl.lowercased()
          // 更全面地识别夸克CDN域名，确保TS片段保留Cookie进行鉴权
          let isQuarkCDN =
            lowercasedTsUrl.contains("pds.quark.cn") ||
            lowercasedTsUrl.contains("video-play") ||
            lowercasedTsUrl.contains("drive.quark.cn") ||
            lowercasedTsUrl.contains("video-h-kdac") ||
            lowercasedTsUrl.contains("quark.cn")

          if !isQuarkCDN {
            // 只有非夸克CDN才移除Cookie
            tsHeaders.removeValue(forKey: "Cookie")
          } else {
            // 夸克CDN增加必要头部
            tsHeaders["Origin"] = "https://pan.quark.cn/"
          }

          let proxyUrl = buildProxyUrl(tsUrl, headers: tsHeaders)
          modifiedLines.append(proxyUrl)
          NSLog("[ProxyServer] 转换 TS URL: 原='\(originalForLog)' 净='\(trimmedLine)' -> 代理='\(proxyUrl)'")
          let bytes = trimmedLine.data(using: .utf8)?.map { String(format: "%02X", $0) }.joined(separator: " ")
          NSLog("字符串十六进制: \(bytes ?? "nil")")
        } else {
          // 其他类型的资源文件
          modifiedLines.append(line)
        }
      } else {
        // 注释行或其他内容保持原样
        modifiedLines.append(line)
      }
    }

    // 使用 CRLF 作为换行，兼容更广泛的 HLS 播放器（如 VLC）
    let result = modifiedLines.joined(separator: "\r\n") + "\r\n"
    NSLog("[ProxyServer] M3U8内容处理完成：原始行数=\(lines.count)，处理后行数=\(modifiedLines.count)")

    // 调试：输出前几行M3U8内容
    let previewLines = modifiedLines.prefix(10)
    NSLog("[ProxyServer] M3U8内容预览：")
    for (index, line) in previewLines.enumerated() {
      NSLog("[ProxyServer]   第\(index+1)行: \(line)")
    }

    // 调试：输出包含proxy的行
    let proxyLines = modifiedLines.filter { $0.contains("/proxy?key=") }
    NSLog("[ProxyServer] 代理URL行数: \(proxyLines.count)")
    if let firstProxyLine = proxyLines.first {
      NSLog("[ProxyServer] 第一个代理URL: \(firstProxyLine)")
    }
    
    return result
  }
  
  private func sendErrorResponse(_ connection: NWConnection, status: Int, message: String) {
    let response = """
      HTTP/1.1 \(status) \(message)
      Content-Type: text/plain
      Content-Length: \(message.count)
      Access-Control-Allow-Origin: *
      
      \(message)
      """.replacingOccurrences(of: "\n", with: "\r\n")
    
    connection.send(content: response.data(using: .utf8), completion: .contentProcessed { _ in
      connection.cancel()
    })
  }
  
  // 为缓存的M3U8响应发送方法
  private func sendCachedM3U8Response(_ connection: NWConnection, data: Data) {
    var responseHeaders = "HTTP/1.1 200 OK\r\n"
    responseHeaders += "Content-Type: application/x-mpegURL\r\n"
    responseHeaders += "Content-Length: \(data.count)\r\n"
    responseHeaders += "Cache-Control: no-cache, no-store, must-revalidate\r\n"
    responseHeaders += "Pragma: no-cache\r\n"
    responseHeaders += "Expires: 0\r\n"
    responseHeaders += "Access-Control-Allow-Origin: *\r\n"
    responseHeaders += "Access-Control-Allow-Methods: GET, POST, OPTIONS\r\n"
    responseHeaders += "Access-Control-Allow-Headers: *\r\n"
    responseHeaders += "Connection: close\r\n"
    responseHeaders += "\r\n"
    
    var finalResponseData = responseHeaders.data(using: .utf8) ?? Data()
    finalResponseData.append(data)
    
    connection.send(content: finalResponseData, completion: .contentProcessed { error in
      if let error = error {
        NSLog("[ProxyServer] 发送缓存M3U8响应失败: \(error)")
      } else {
        NSLog("[ProxyServer] 发送缓存M3U8响应成功: \(data.count) 字节")
      }
      // M3U8 文件发送完成后延迟关闭连接，确保数据完全传输
      DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
        connection.cancel()
      }
    })
  }
}

// String MD5 扩展
extension String {
  var md5: String {
    let data = Data(self.utf8)
    let hash = data.withUnsafeBytes { bytes -> [UInt8] in
      var hash = [UInt8](repeating: 0, count: Int(CC_MD5_DIGEST_LENGTH))
      CC_MD5(bytes.bindMemory(to: UInt8.self).baseAddress, CC_LONG(data.count), &hash)
      return hash
    }
    return hash.map { String(format: "%02x", $0) }.joined()
  }
}

// MARK: - VLC Player NSView (macOS PlatformView returns NSView)
final class VLCPlayerView: NSView {
  private let videoView: VLCVideoView
  private let mediaPlayer: VLCMediaPlayer
  private let methodChannel: FlutterMethodChannel
  private let eventChannel: FlutterEventChannel
  private var eventSink: FlutterEventSink?
  private var positionTimer: Timer?

  init(frame: CGRect, viewId: Int64, args: Any?, messenger: FlutterBinaryMessenger) {
    self.videoView = VLCVideoView(frame: frame)
    self.mediaPlayer = VLCMediaPlayer()
    self.methodChannel = FlutterMethodChannel(name: "free_tv/vlc_player_\(viewId)", binaryMessenger: messenger)
    self.eventChannel = FlutterEventChannel(name: "free_tv/vlc_player_events_\(viewId)", binaryMessenger: messenger)
    super.init(frame: frame)

    wantsLayer = true
    videoView.autoresizingMask = [.width, .height]
    videoView.translatesAutoresizingMaskIntoConstraints = true
    addSubview(videoView)

    mediaPlayer.drawable = videoView
    mediaPlayer.delegate = self

    methodChannel.setMethodCallHandler { [weak self] call, result in
      guard let self = self else { return }
      switch call.method {
      case "initialize":
        guard let dict = call.arguments as? [String: Any], let urlStr = dict["url"] as? String, let url = URL(string: urlStr) else {
          result(FlutterError(code: "400", message: "missing url", details: nil)); return
        }
        let media = VLCMedia(url: url)
        // 增加网络缓存时间，特别是对于代理播放
        media.addOption(":network-caching=5000")
        media.addOption(":http-reconnect=true")
        // HLS特定优化
        media.addOption(":hls-segment-length=10")
        // 增加文件缓存
        media.addOption(":file-caching=2000")
        // 设置更激进的缓存策略
        media.addOption(":clock-jitter=0")
        NSLog("[VLC] 配置媒体播放器: 网络缓存=5s, 文件缓存=2s")
        if let headers = dict["headers"] as? [String: String], !headers.isEmpty {
          if let ua = headers["User-Agent"] { media.addOption(":http-user-agent=\(ua)") }
          if let ref = headers["Referer"] { media.addOption(":http-referrer=\(ref)") }
          if let ck = headers["Cookie"] { media.addOption(":http-cookie=\(ck)") }
        }
        self.mediaPlayer.media = media
        result(["ok": true])

      case "play":
        self.mediaPlayer.play()
        self.startPositionTimer()
        result(["ok": true])

      case "pause":
        self.mediaPlayer.pause()
        self.stopPositionTimer()
        result(["ok": true])

      case "seekTo":
        guard let dict = call.arguments as? [String: Any], let ms = dict["positionMs"] as? Int64 else {
          result(FlutterError(code: "400", message: "missing positionMs", details: nil)); return
        }
        self.mediaPlayer.time = VLCTime(number: NSNumber(value: ms))
        result(["ok": true])

      case "setRate":
        guard let dict = call.arguments as? [String: Any], let rate = dict["rate"] as? Double else {
          result(FlutterError(code: "400", message: "missing rate", details: nil)); return
        }
        self.mediaPlayer.rate = Float(rate)
        result(["ok": true])

      case "setVolume":
        guard let dict = call.arguments as? [String: Any], let volume = dict["volume"] as? Double else {
          result(FlutterError(code: "400", message: "missing volume", details: nil)); return
        }
        let v = Int32(max(0, min(1, volume)) * 200)
        self.mediaPlayer.audio?.volume = v
        result(["ok": true])

      case "dispose":
        self.stopPositionTimer()
        self.mediaPlayer.stop()
        self.mediaPlayer.drawable = nil
        result(["ok": true])

      default:
        result(FlutterMethodNotImplemented)
      }
    }

    eventChannel.setStreamHandler(self)

    if let dict = args as? [String: Any], let urlStr = dict["url"] as? String, let url = URL(string: urlStr) {
      let media = VLCMedia(url: url)
      // 增加网络缓存时间，特别是对于代理播放
      media.addOption(":network-caching=5000")
      media.addOption(":http-reconnect=true")
      // HLS特定优化
      media.addOption(":hls-segment-length=10")
      // 增加文件缓存
      media.addOption(":file-caching=2000")
      // 设置更激进的缓存策略
      media.addOption(":clock-jitter=0")
      NSLog("[VLC] 配置媒体播放器: 网络缓存=5s, 文件缓存=2s")
      if let headers = dict["headers"] as? [String: String], !headers.isEmpty {
        if let ua = headers["User-Agent"] { media.addOption(":http-user-agent=\(ua)") }
        if let ref = headers["Referer"] { media.addOption(":http-referrer=\(ref)") }
        if let ck = headers["Cookie"] { media.addOption(":http-cookie=\(ck)") }
      }
      mediaPlayer.media = media
    }
  }

  required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }

  private func startPositionTimer() {
    stopPositionTimer()
    positionTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
      guard let self = self, let sink = self.eventSink else { return }
      let durationMs = self.mediaPlayer.media?.length.intValue ?? -1
      let positionMs = self.mediaPlayer.time.intValue
      let state = self.mediaPlayer.state.rawValue
      sink(["event": "onPosition", "positionMs": positionMs, "durationMs": durationMs, "state": state])
    }
  }

  private func stopPositionTimer() {
    positionTimer?.invalidate()
    positionTimer = nil
  }
}

extension VLCPlayerView: VLCMediaPlayerDelegate {
  func mediaPlayerStateChanged(_ aNotification: Notification) {
    guard let sink = eventSink else { return }
    let state = mediaPlayer.state.rawValue
    let durationMs = mediaPlayer.media?.length.intValue ?? -1
    let positionMs = mediaPlayer.time.intValue
    sink(["event": "onState", "state": state, "positionMs": positionMs, "durationMs": durationMs])
  }

  func mediaPlayerTimeChanged(_ aNotification: Notification) { /* timer handles */ }
}

extension VLCPlayerView: FlutterStreamHandler {
  func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
    self.eventSink = events
    let durationMs = mediaPlayer.media?.length.intValue ?? -1
    events(["event": "onInitialized", "durationMs": durationMs])
    return nil
  }

  func onCancel(withArguments arguments: Any?) -> FlutterError? {
    self.eventSink = nil
    return nil
  }
}

final class VLCPlayerFactory: NSObject, FlutterPlatformViewFactory {
  private let messenger: FlutterBinaryMessenger
  init(messenger: FlutterBinaryMessenger) {
    self.messenger = messenger
    super.init()
  }
  func createArgsCodec() -> (FlutterMessageCodec & NSObjectProtocol)? { FlutterStandardMessageCodec.sharedInstance() }
  func create(withViewIdentifier viewId: Int64, arguments args: Any?) -> NSView {
    VLCPlayerView(frame: .zero, viewId: viewId, args: args, messenger: messenger)
  }
}

class MainFlutterWindow: NSWindow {
  override func awakeFromNib() {
    let flutterViewController = FlutterViewController()
    let windowFrame = self.frame
    self.contentViewController = flutterViewController
    self.setFrame(windowFrame, display: true)

    RegisterGeneratedPlugins(registry: flutterViewController)
    
    // 启动 HTTP 代理服务器
    ProxyServer.shared.start()

    // 注册 VLC Player PlatformView 工厂
    let registrar = flutterViewController.engine.registrar(forPlugin: "VLCPlayerPlugin")
    registrar.register(VLCPlayerFactory(messenger: registrar.messenger), withId: "free_tv/vlc_player")

    // 注册"free_tv/quark" MethodChannel（内联实现，避免 Xcode 目标未包含自定义 Swift 文件导致的构建失败）
    let messenger = flutterViewController.engine.binaryMessenger
    let quarkChannel = FlutterMethodChannel(name: "free_tv/quark", binaryMessenger: messenger)
    quarkChannel.setMethodCallHandler { call, result in
      switch call.method {
      case "init":
        // 保存 Cookie（简单存储到 UserDefaults）
        if let args = call.arguments as? [String: Any], let cookie = args["cookie"] as? String {
          UserDefaults.standard.set(cookie, forKey: "free_tv_quark_cookie")
          result(["ok": true])
        } else {
          result(FlutterError(code: "400", message: "参数错误：缺少 cookie", details: nil))
        }

      case "detail":
        // 改进：对"目录型夸克分享链接"进行目录枚举，返回真实分集
        guard let args = call.arguments as? [String: Any],
              let links = args["links"] as? [String] else {
          result(FlutterError(code: "400", message: "参数错误：缺少 links", details: nil))
          break
        }
        let pwds = (args["pwds"] as? [String: String]) ?? [:]
        let cookie = UserDefaults.standard.string(forKey: "free_tv_quark_cookie") ?? ""
        if cookie.isEmpty {
          result(FlutterError(code: "401", message: "未登录夸克，请先在设置中扫码登录", details: nil))
          break
        }
        
        // 调试：检查 cookie 是否包含必要的认证信息
        NSLog("[Quark.macOS] cookie length=\(cookie.count), contains __pus=\(cookie.contains("__pus")), contains __puus=\(cookie.contains("__puus"))")
        if !cookie.contains("__pus") && !cookie.contains("__puus") {
          result(FlutterError(code: "401", message: "夸克登录已过期，请重新扫码登录", details: nil))
          break
        }

        func headers() -> [String: String] {
          // 与 Android 对齐的最小必要头：不带 Origin / X-Requested-With / Host
          let ck = UserDefaults.standard.string(forKey: "free_tv_quark_cookie") ?? ""
          let h: [String: String] = [
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/2.5.20 Chrome/100.0.4896.160 Electron/********-b478491100 Safari/537.36 Channel/pckk_other_ch",
            "Referer": "https://pan.quark.cn/",
            "Content-Type": "application/json",
            "Cookie": ck,
            // 添加浏览器指纹头部增强认证
            "Sec-Ch-Ua": "\"Chromium\";v=\"100\", \"Not(A:Brand\";v=\"8\", \"Google Chrome\";v=\"100\"",
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": "\"Windows\"",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            // 注意：不设置Host头部，让HTTP客户端自动处理
          ]
          // 打印 cookie 关键名，便于排查
          let names = ck.split(separator: ";").map { $0.trimmingCharacters(in: .whitespaces) }.map { $0.split(separator: "=").first.map(String.init) ?? "" }
          NSLog("[Quark.macOS] headers keys=\(Array(h.keys)) cookie.names=\(names)")
          return h
        }
        func extractShareId(_ url: String) -> String? {
          guard let range = url.range(of: "pan.quark.cn/s/") else { return nil }
          let sub = url[range.upperBound...]
          var id = ""
          for ch in sub {
            if ch == "/" || ch == "#" || ch == "?" { break }
            id.append(ch)
          }
          NSLog("[Quark.macOS] extractShareId from url=\(url) -> id=\(id)")
          return id.isEmpty ? nil : id
        }
        // 兼容旧系统（不使用 async/await）：同步请求 + 后台队列
        func apiRequestSync(path: String, method: String = "GET", query: [String: String] = [:], body: [String: Any]? = nil) throws -> [String: Any] {
          func encodeURIComponent(_ s: String) -> String {
            var allowed = CharacterSet.alphanumerics
            allowed.insert(charactersIn: "-_.~")
            return s.addingPercentEncoding(withAllowedCharacters: allowed) ?? s
          }
          var comps = URLComponents(string: "https://drive-pc.quark.cn/1/clouddrive/\(path)")!
          if !query.isEmpty {
            let q = query.map { "\(encodeURIComponent($0.key))=\(encodeURIComponent($0.value))" }.joined(separator: "&")
            comps.percentEncodedQuery = q
          }
          var req = URLRequest(url: comps.url!)
          req.httpMethod = method
          req.httpShouldHandleCookies = false
          let h = headers(); h.forEach { req.setValue($0.value, forHTTPHeaderField: $0.key) }
          if let body = body {
            req.httpBody = try? JSONSerialization.data(withJSONObject: body)
            req.setValue("application/json", forHTTPHeaderField: "Content-Type")
          }
          NSLog("[Quark.macOS] apiRequestSync path=\(path) method=\(method) url=\(comps.url!.absoluteString)")
          let sema = DispatchSemaphore(value: 0)
          var dataOut: Data?; var status: Int = -1; var err: Error?; var respHeaders: [AnyHashable: Any] = [:]
          URLSession.shared.dataTask(with: req) { data, resp, error in
            dataOut = data
            if let http = resp as? HTTPURLResponse {
              status = http.statusCode
              respHeaders = http.allHeaderFields
            } else {
              status = -1
            }
            err = error
            sema.signal()
          }.resume()
          _ = sema.wait(timeout: .now() + 30)
           // 先合并响应 Cookie（即便非 200），以便下次重试生效
          var headerStrings: [String: String] = [:]
          for (k, v) in respHeaders { headerStrings[String(describing: k)] = String(describing: v) }
          if let url = comps.url {
            let cookies = HTTPCookie.cookies(withResponseHeaderFields: headerStrings, for: url)
            var stored = UserDefaults.standard.string(forKey: "free_tv_quark_cookie") ?? ""
            for c in cookies {
              let pair = "\(c.name)=\(c.value)"
              if let r = stored.range(of: "\(c.name)=[^;]+", options: .regularExpression) {
                stored.replaceSubrange(r, with: pair)
              } else {
                stored += (stored.isEmpty ? "" : ";") + pair
              }
              // 注入到系统 Cookie 存储，便于 AVPlayer (video_player) 自动携带 Cookie 访问 CDN
              HTTPCookieStorage.shared.setCookie(c)
              NSLog("[Quark.macOS] merged cookie name=\(c.name) domain=\(c.domain)")
            }
            UserDefaults.standard.set(stored, forKey: "free_tv_quark_cookie")
          }
           // 打印响应体（成功或失败都打印）
           let bodyStr: String = {
             if let d = dataOut, let s = String(data: d, encoding: .utf8) { return s } else { return "<no-body>" }
           }()
           NSLog("[Quark.macOS] apiResponse path=\(path) status=\(status) body=\(bodyStr)")
          if let e = err { throw e }
          guard status == 200, let d = dataOut else { throw NSError(domain: "http", code: status) }
          let obj = (try? JSONSerialization.jsonObject(with: d)) as? [String: Any] ?? [:]
          return obj
        }
        func getStokenSync(shareId: String, passcode: String?) throws -> String {
          NSLog("[Quark.macOS] getStokenSync shareId=\(shareId) passcode.len=\(passcode?.count ?? 0)")
          let json = try apiRequestSync(
            path: "share/sharepage/token",
            method: "POST",
            query: ["pr": "ucpro", "fr": "pc"],
            body: ["pwd_id": shareId, "passcode": passcode ?? ""]
          )
          let data = (json["data"] as? [String: Any]) ?? [:]
          let st = (data["stoken"] as? String) ?? ""
          NSLog("[Quark.macOS] getStokenSync ok?=\(!st.isEmpty) stoken.len=\(st.count)")
          return st
        }
        func listAllSync(shareId: String, stoken: String, folderId: String = "0") throws -> [[String: Any]] {
          var all: [[String: Any]] = []
          let prePage = 200
          var page = 1
          while true {
            let q: [String: String] = [
              "pr": "ucpro", "fr": "pc", "pwd_id": shareId,
              // 使用 encodeURIComponent 规则编码 stoken，避免 "+" 被解释为空格
              "stoken": stoken,
              "pdir_fid": folderId, "force": "0",
              "_page": "\(page)", "_size": "\(prePage)", "_sort": "file_type:asc,file_name:asc",
            ]
            let json = try apiRequestSync(path: "share/sharepage/detail", query: q)
            guard let data = json["data"] as? [String: Any], let list = data["list"] as? [[String: Any]] else { break }
            var subDirs: [[String: Any]] = []
            for item in list {
              if let isDir = item["dir"] as? Bool, isDir {
                subDirs.append(item)
              } else if let isFile = item["file"] as? Bool, isFile, (item["obj_category"] as? String) == "video" {
                let size = (item["size"] as? NSNumber)?.doubleValue ?? 0
                if size < 5 * 1024 * 1024 { continue }
                let fid = (item["fid"] as? String) ?? ""
                let token = (item["share_fid_token"] as? String) ?? ""
                let name = (item["file_name"] as? String) ?? fid
                let id = "\(fid)++\(token)++\(shareId)++\(stoken)"
                all.append(["name": name, "url": id])
              }
            }
            // 先递归子目录（即使当前是最后一页也要递归）
            if !subDirs.isEmpty { NSLog("[Quark.macOS] listAllSync subDirs=\(subDirs.count) -> recurse") }
            for dir in subDirs {
              let fid = String(describing: dir["fid"] ?? "0")
              let subItems = try listAllSync(shareId: shareId, stoken: stoken, folderId: fid)
              all.append(contentsOf: subItems)
            }
            // 再处理翻页
            if let meta = json["metadata"] as? [String: Any], let total = meta["_total"] as? NSNumber {
              let totalPages = Int(ceil(total.doubleValue / Double(prePage)))
              NSLog("[Quark.macOS] listAllSync page=\(page)/\(totalPages) items=\(list.count) acc=\(all.count)")
              if page < totalPages { page += 1 } else { break }
            } else { break }
          }
          return all
        }

        // 后台线程执行，避免阻塞 UI
        DispatchQueue.global().async {
          var episodes: [[String: Any]] = []
          var seen = Set<String>()
          for (idx, l) in links.enumerated() {
            NSLog("[Quark.macOS] detail process link[\(idx)]=\(l)")
            guard let sid = extractShareId(l) else { NSLog("[Quark.macOS] invalid link -> skip"); continue }
            let pwd = pwds[l]
            do {
              let stoken = try getStokenSync(shareId: sid, passcode: pwd)
              if stoken.isEmpty { NSLog("[Quark.macOS] empty stoken -> skip"); continue }
              let list = try listAllSync(shareId: sid, stoken: stoken)
              for ep in list {
                if let url = ep["url"] as? String, !seen.contains(url) {
                  seen.insert(url); episodes.append(ep)
                }
              }
            } catch {
              NSLog("[Quark] detail error for shareId=\(sid): \(error.localizedDescription)")
            }
          }
          episodes.sort { (a, b) -> Bool in
            let na = (a["name"] as? String) ?? ""
            let nb = (b["name"] as? String) ?? ""
            return na.localizedStandardCompare(nb) == .orderedAscending
          }
          NSLog("[Quark.macOS] detail done episodes=\(episodes.count)")
          DispatchQueue.main.async {
            result([
              "name": "quark",
              "seriesFlags": ["quark原画"],
              "seriesMap": ["quark原画": episodes],
            ])
          }
        }

      case "playerContent":
        // 实现夸克直链/转码取流
        guard let args = call.arguments as? [String: Any], let id = args["id"] as? String, let flag = args["flag"] as? String else {
          result(FlutterError(code: "400", message: "参数错误：缺少 id/flag", details: nil)); break
        }
        let lower = id.lowercased()
        if lower.hasSuffix(".mp4") || lower.hasSuffix(".m3u8") || lower.contains(".m3u8?") || lower.contains(".mp4?") {
          result(["url": id, "header": ["User-Agent": "Mozilla/5.0"], "parse": "0"]) ; break
        }
        // 解析 id: fid++fileToken++shareId++stoken
        let parts = id.components(separatedBy: "++")
        if parts.count < 4 { result(FlutterError(code: "400", message: "id 非法", details: id)); break }
        let fid = parts[0], fileToken = parts[1], shareId = parts[2], stoken = parts[3]

        func headers() -> [String: String] {
          let ck = UserDefaults.standard.string(forKey: "free_tv_quark_cookie") ?? ""
          let h: [String: String] = [
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/2.5.20 Chrome/100.0.4896.160 Electron/********-b478491100 Safari/537.36 Channel/pckk_other_ch",
            "Referer": "https://pan.quark.cn/",
            "Accept": "*/*",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Cookie": ck,
            // 添加浏览器指纹头部增强认证
            "Sec-Ch-Ua": "\"Chromium\";v=\"100\", \"Not(A:Brand\";v=\"8\", \"Google Chrome\";v=\"100\"",
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": "\"Windows\"",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            // 注意：根据Android版本，不设置Host和Content-Type头部，让HTTP客户端自动处理
          ]
          let names = ck.split(separator: ";").map { $0.trimmingCharacters(in: .whitespaces) }.map { $0.split(separator: "=").first.map(String.init) ?? "" }
          NSLog("[Quark.macOS] [playerContent] headers keys=\(Array(h.keys)) cookie.names=\(names)")
          return h
        }
        func apiRequestSync(path: String, method: String = "GET", query: [String: String] = [:], body: [String: Any]? = nil) throws -> [String: Any] {
          func encodeURIComponent(_ s: String) -> String {
            var allowed = CharacterSet.alphanumerics
            allowed.insert(charactersIn: "-_.~")
            return s.addingPercentEncoding(withAllowedCharacters: allowed) ?? s
          }
          var comps = URLComponents(string: "https://drive-pc.quark.cn/1/clouddrive/\(path)")!
          if !query.isEmpty {
            let q = query.map { "\(encodeURIComponent($0.key))=\(encodeURIComponent($0.value))" }.joined(separator: "&")
            comps.percentEncodedQuery = q
          }
          let url = comps.url!
          var req = URLRequest(url: url)
          req.httpMethod = method
          req.httpShouldHandleCookies = false
          let h = headers(); h.forEach { req.setValue($0.value, forHTTPHeaderField: $0.key) }
          if let body = body {
            req.httpBody = try? JSONSerialization.data(withJSONObject: body)
            req.setValue("application/json", forHTTPHeaderField: "Content-Type")
          }
          NSLog("%@", "[Quark.macOS] apiRequestSync path=\(path) method=\(method) url=\(url.absoluteString)")
          let sema = DispatchSemaphore(value: 0)
          var dataOut: Data?; var status: Int = -1; var respHeaders: [AnyHashable: Any] = [:]
          URLSession.shared.dataTask(with: req) { data, resp, _ in
            dataOut = data
            if let http = resp as? HTTPURLResponse {
              status = http.statusCode
              respHeaders = http.allHeaderFields
            } else {
              status = -1
            }
            sema.signal()
          }.resume()
          _ = sema.wait(timeout: .now() + 30)
          // 解析响应 Cookie，合并所有 Cookie（即便非 200 也要合并，以便下次重试）
          var headerStrings: [String: String] = [:]
          for (k, v) in respHeaders { headerStrings[String(describing: k)] = String(describing: v) }
          let cookies = HTTPCookie.cookies(withResponseHeaderFields: headerStrings, for: url)
          var stored = UserDefaults.standard.string(forKey: "free_tv_quark_cookie") ?? ""
          for c in cookies {
            let pair = "\(c.name)=\(c.value)"
            if let r = stored.range(of: "\(c.name)=[^;]+", options: .regularExpression) {
              stored.replaceSubrange(r, with: pair)
            } else {
              stored += (stored.isEmpty ? "" : ";") + pair
            }
            NSLog("%@", "[Quark.macOS] merged cookie name=\(c.name)")
          }
          UserDefaults.standard.set(stored, forKey: "free_tv_quark_cookie")
          let bodyStr: String = {
            if let d = dataOut, let s = String(data: d, encoding: .utf8) { return s } else { return "<no-body>" }
          }()
          NSLog("[Quark.macOS] [playerContent] apiResponse path=\(path) status=\(status) body=\(bodyStr)")
          guard status == 200, let d = dataOut else { throw NSError(domain: "http", code: status) }
          let obj = (try? JSONSerialization.jsonObject(with: d)) as? [String: Any] ?? [:]
          return obj
        }
        // 刷新分享详情以唤醒 Cookie（避免后续 401）
        func refreshShareDetail(shareId: String, stoken: String) {
          _ = try? apiRequestSync(
            path: "share/sharepage/detail",
            query: [
              "pr": "ucpro", "fr": "pc", "pwd_id": shareId,
              "stoken": stoken, "pdir_fid": "0", "_page": "1", "_size": "1", "_sort": "file_type:asc,file_name:asc"
            ]
          )
        }
        // 列出用户目录下文件
        func listSelfDir(pdir: String, page: Int = 1, size: Int = 200) throws -> [[String: Any]] {
          // 检查登录状态
          let currentCookie = UserDefaults.standard.string(forKey: "free_tv_quark_cookie") ?? ""
          if currentCookie.isEmpty || (!currentCookie.contains("__pus") && !currentCookie.contains("__puus")) {
            throw NSError(domain: "auth", code: 401, userInfo: [NSLocalizedDescriptionKey: "未登录或登录已过期"])
          }
          
          let q: [String: String] = [
            "pr": "ucpro", "fr": "pc",
            "pdir_fid": pdir, "_page": "\(page)", "_size": "\(size)", "_sort": "file_type:asc,file_name:asc",
          ]
          do {
            let json = try apiRequestSync(path: "file/sort", query: q)
            let data = (json["data"] as? [String: Any]) ?? [:]
            return (data["list"] as? [[String: Any]]) ?? []
          } catch let e as NSError where e.domain == "http" && e.code == 401 {
            // 401 时先去刷新分享详情以唤醒 Cookie，再重试一次
            refreshShareDetail(shareId: shareId, stoken: stoken)
            let json = try apiRequestSync(path: "file/sort", query: q)
            let data = (json["data"] as? [String: Any]) ?? [:]
            return (data["list"] as? [[String: Any]]) ?? []
          }
        }
        func findChildByName(pdir: String, name: String, isDir: Bool) throws -> [String: Any]? {
          var page = 1
          while true {
            let list = try listSelfDir(pdir: pdir, page: page)
            if list.isEmpty { return nil }
            for item in list {
              let dir = (item["dir"] as? Bool) ?? false
              let fileName = (item["file_name"] as? String) ?? ""
              if dir == isDir && fileName == name { return item }
            }
            if list.count < 200 { break }
            page += 1
          }
          return nil
        }
        func createFolder(pdir: String, name: String) throws -> String {
          let query = ["pr": "ucpro", "fr": "pc"]
          let body: [String: Any] = ["pdir_fid": pdir, "file_name": name, "dir": true]
          do {
            let json = try apiRequestSync(path: "file", method: "POST", query: query, body: body)
            if let data = json["data"] as? [String: Any], let fid = data["fid"] as? String { return fid }
          } catch let e as NSError where e.domain == "http" && e.code == 401 {
            refreshShareDetail(shareId: shareId, stoken: stoken)
            let json = try apiRequestSync(path: "file", method: "POST", query: query, body: body)
            if let data = json["data"] as? [String: Any], let fid = data["fid"] as? String { return fid }
          }
          throw NSError(domain: "mkdir", code: -1)
        }
        func ensureTempDir() throws -> String {
          let root = "0"
          if let hit = try findChildByName(pdir: root, name: "temp", isDir: true), let fid = hit["fid"] as? String { return fid }
          return try createFolder(pdir: root, name: "temp")
        }
        // 保存分享文件到指定目录
        func saveShareToDir(shareId: String, stoken: String, srcFid: String, srcToken: String, toPdir: String) throws -> String {
          let query = ["pr": "ucpro", "fr": "pc"]
          let body: [String: Any] = [
            "pwd_id": shareId,
            "stoken": stoken,
            "to_pdir_fid": toPdir,
            "fid_list": [srcFid],
            "fid_token_list": [srcToken],
            // 与 Android 对齐：补充 pdir_fid/scene，提高兼容性
            "pdir_fid": "0",
            "scene": "link",
          ]
          do {
            let json = try apiRequestSync(path: "share/sharepage/save", method: "POST", query: query, body: body)
            if let data = json["data"] as? [String: Any], let taskId = data["task_id"] as? String { return taskId }
          } catch let e as NSError where e.domain == "http" && e.code == 401 {
            refreshShareDetail(shareId: shareId, stoken: stoken)
            let json = try apiRequestSync(path: "share/sharepage/save", method: "POST", query: query, body: body)
            if let data = json["data"] as? [String: Any], let taskId = data["task_id"] as? String { return taskId }
          }
          throw NSError(domain: "save", code: -1)
        }
        func pollTask(taskId: String, timeoutSec: Int = 30) throws -> [String: Any] {
          let start = Date().timeIntervalSince1970
          while true {
            do {
              let json = try apiRequestSync(path: "task", query: ["pr": "ucpro", "fr": "pc", "task_id": taskId])
              if let data = json["data"] as? [String: Any] {
                // 兼容不同返回结构，优先 save_as_top_fids
                if let fids = data["save_as_top_fids"] as? [String], !fids.isEmpty { return ["fid": fids[0]] }
                // 一些返回在 data.save_as.save_as_top_fids
                if let saveAs = data["save_as"] as? [String: Any], let fids2 = saveAs["save_as_top_fids"] as? [String], !fids2.isEmpty { return ["fid": fids2[0]] }
                if let status = (data["status"] as? String)?.lowercased(), status == "finished" || status == "success" {
                  if let fids = data["save_as_top_fids"] as? [String], !fids.isEmpty { return ["fid": fids[0]] }
                  if let saveAs = data["save_as"] as? [String: Any], let fids2 = saveAs["save_as_top_fids"] as? [String], !fids2.isEmpty { return ["fid": fids2[0]] }
                }
              }
            } catch let e as NSError where e.domain == "http" && e.code == 401 {
              // 401 唤醒后继续轮询
              refreshShareDetail(shareId: shareId, stoken: stoken)
            }
            if Date().timeIntervalSince1970 - start > Double(timeoutSec) { throw NSError(domain: "task_timeout", code: -1) }
            Thread.sleep(forTimeInterval: 0.8)
          }
        }
        // 简单缓存，避免重复保存
        func savedMapGet() -> [String: String] {
          if let raw = UserDefaults.standard.string(forKey: "free_tv_quark_saved_map"), let data = raw.data(using: .utf8) {
            do {
              if let obj = try JSONSerialization.jsonObject(with: data) as? [String: String] { return obj }
            } catch {
              // JSON解析失败，返回空字典
            }
          }
          return [:]
        }
        func savedMapSet(_ map: [String: String]) {
          if let data = try? JSONSerialization.data(withJSONObject: map), let raw = String(data: data, encoding: .utf8) {
            UserDefaults.standard.set(raw, forKey: "free_tv_quark_saved_map")
          }
        }
        func ensureSavedAndGetSelfFid(shareId: String, stoken: String, srcFid: String, srcToken: String) throws -> String {
          let cacheKey = "\(shareId)::\(srcFid)::\(srcToken)"
          var map = savedMapGet()
          if let hit = map[cacheKey] { return hit }
          refreshShareDetail(shareId: shareId, stoken: stoken)
          // 保存到 temp 目录，与 Android 代码对齐
          let tempDir = try ensureTempDir()
          let taskId = try saveShareToDir(shareId: shareId, stoken: stoken, srcFid: srcFid, srcToken: srcToken, toPdir: tempDir)
          let res = try pollTask(taskId: taskId)
          let selfFid = (res["fid"] as? String) ?? ""
          if !selfFid.isEmpty { map[cacheKey] = selfFid; savedMapSet(map) }
          return selfFid
        }

        func selfDownloadURL(fid: String) throws -> String {
          let query = ["pr": "ucpro", "fr": "pc", "uc_param_str": ""]
          let body: [String: Any] = ["fids": [fid]]
          do {
            let json = try apiRequestSync(path: "file/download", method: "POST", query: query, body: body)
            if let arr = json["data"] as? [[String: Any]], let first = arr.first, let url = first["download_url"] as? String { return url }
          } catch let e as NSError where e.domain == "http" && e.code == 401 {
            refreshShareDetail(shareId: shareId, stoken: stoken)
            let json = try apiRequestSync(path: "file/download", method: "POST", query: query, body: body)
            if let arr = json["data"] as? [[String: Any]], let first = arr.first, let url = first["download_url"] as? String { return url }
          }
          throw NSError(domain: "download", code: -1)
        }
        // 分享直取：尝试无需转存直接获取原画直链
        func shareDownloadURL(shareId: String, stoken: String, srcFid: String, srcToken: String) throws -> String {
          let query = ["pr": "ucpro", "fr": "pc"]
          // 尝试两种入参格式
          let bodies: [[String: Any]] = [
            ["pwd_id": shareId, "stoken": stoken, "fid": srcFid, "share_fid_token": srcToken],
            ["pwd_id": shareId, "stoken": stoken, "fid_list": [srcFid], "fid_token_list": [srcToken]]
          ]
          for (idx, body) in bodies.enumerated() {
            do {
              let json = try apiRequestSync(path: "share/sharepage/download", method: "POST", query: query, body: body)
              if let data = json["data"] as? [String: Any], let url = data["download_url"] as? String { return url }
            } catch let e as NSError where e.domain == "http" && e.code == 401 {
              NSLog("%@", "[Quark.macOS] shareDownloadURL 401 bodyIdx=\(idx) -> refresh & retry")
              refreshShareDetail(shareId: shareId, stoken: stoken)
              let json = try apiRequestSync(path: "share/sharepage/download", method: "POST", query: query, body: body)
              if let data = json["data"] as? [String: Any], let url = data["download_url"] as? String { return url }
            } catch {
              // 尝试下一个 body 结构
            }
          }
          throw NSError(domain: "share_download", code: -1)
        }
        func selfTranscodeURL(fid: String, flag: String) throws -> String {
          let query = ["pr": "ucpro", "fr": "pc"]
          let body: [String: Any] = [
            "fid": fid,
            "resolutions": "normal,low,high,super,2k,4k",
            "supports": "fmp4"
          ]
          func pickURL(_ play: [String: Any]) -> String? {
            guard let data = play["data"] as? [String: Any], let vlist = data["video_list"] as? [[String: Any]] else { return nil }
            let prefer: [String] = ["4k", "2k", "super", "high", "normal", "low"]
            func wantedFromFlag(_ name: String) -> String? {
              let idxMap: [String: String] = ["4K": "4k", "超清": "super", "高清": "high", "普画": "low"]
              return idxMap.first(where: { name.contains($0.key) })?.value
            }
            let available: Set<String> = Set(vlist.compactMap { $0["resolution"] as? String })
            let order: [String]
            if let wanted = wantedFromFlag(flag), available.contains(wanted) {
              order = [wanted] + prefer.filter { $0 != wanted }
            } else {
              order = prefer
            }
            for res in order {
              if let v = vlist.first(where: { ($0["resolution"] as? String) == res }), let info = v["video_info"] as? [String: Any], let url = info["url"] as? String {
                return url
              }
            }
            return vlist.compactMap { ($0["video_info"] as? [String: Any])?["url"] as? String }.first
          }
          do {
            let play = try apiRequestSync(path: "file/v2/play", method: "POST", query: query, body: body)
            if let u = pickURL(play) { return u }
          } catch let e as NSError where e.domain == "http" && e.code == 401 {
            refreshShareDetail(shareId: shareId, stoken: stoken)
            let play = try apiRequestSync(path: "file/v2/play", method: "POST", query: query, body: body)
            if let u = pickURL(play) { return u }
          }
          throw NSError(domain: "play_self", code: -1)
        }
        // 分享直取：尝试无需转存直接获取转码链接（PC API 可能不可用，保留作为兜底尝试）
        func shareTranscodeURL(shareId: String, stoken: String, srcFid: String, srcToken: String, flag: String) throws -> String {
          let query = ["pr": "ucpro", "fr": "pc"]
          let bodyBase: [String: Any] = [
            "pwd_id": shareId,
            "stoken": stoken,
            "fid": srcFid,
            "share_fid_token": srcToken,
            "resolutions": "normal,low,high,super,2k,4k",
            "supports": "fmp4"
          ]
          func pickURL(_ play: [String: Any]) -> String? {
            guard let data = play["data"] as? [String: Any], let vlist = data["video_list"] as? [[String: Any]] else { return nil }
            let prefer: [String] = ["4k", "2k", "super", "high", "normal", "low"]
            func wantedFromFlag(_ name: String) -> String? {
              let idxMap: [String: String] = ["4K": "4k", "超清": "super", "高清": "high", "普画": "low"]
              return idxMap.first(where: { name.contains($0.key) })?.value
            }
            let available: Set<String> = Set(vlist.compactMap { $0["resolution"] as? String })
            let order: [String]
            if let wanted = wantedFromFlag(flag), available.contains(wanted) {
              order = [wanted] + prefer.filter { $0 != wanted }
            } else {
              order = prefer
            }
            for res in order {
              if let v = vlist.first(where: { ($0["resolution"] as? String) == res }), let info = v["video_info"] as? [String: Any], let url = info["url"] as? String {
                return url
              }
            }
            return vlist.compactMap { ($0["video_info"] as? [String: Any])?["url"] as? String }.first
          }
          // 尝试两个路径
          let paths = ["share/sharepage/v2/play", "share/sharepage/play"]
          for (idx, path) in paths.enumerated() {
            do {
              let play = try apiRequestSync(path: path, method: "POST", query: query, body: bodyBase)
              if let u = pickURL(play) { return u }
            } catch let e as NSError where e.domain == "http" && e.code == 401 {
              NSLog("%@", "[Quark.macOS] shareTranscodeURL 401 pathIdx=\(idx) -> refresh & retry")
              refreshShareDetail(shareId: shareId, stoken: stoken)
              let play = try apiRequestSync(path: path, method: "POST", query: query, body: bodyBase)
              if let u = pickURL(play) { return u }
            } catch {
              // 尝试下一个路径
            }
          }
          throw NSError(domain: "play_share", code: -1)
        }

        // 登录校验：若首次 401，先刷新分享详情以唤醒 cookie，再重试一次
        func validateLogin() -> Bool {
          func checkOnce() -> Bool {
            do {
              _ = try apiRequestSync(
                path: "member",
                query: [
                  "pr": "ucpro",
                  "fr": "pc",
                  "uc_param_str": "",
                  "fetch_subscribe": "true",
                  "_ch": "home",
                  "fetch_identity": "true",
                ]
              )
              return true
            } catch {
              return false
            }
          }
          if checkOnce() { return true }
          NSLog("[Quark.macOS] validateLogin first check failed -> refreshShareDetail & retry")
          refreshShareDetail(shareId: shareId, stoken: stoken)
          return checkOnce()
        }

        DispatchQueue.global().async {
          func sanitizeUrl(_ raw: String) -> String {
            // 最小化处理：仅将空格替换为 %20，避免播放器解析失败
            // 不做二次编码，防止已存在的 %XX 被破坏
            return raw.replacingOccurrences(of: " ", with: "%20")
          }
          
          // 强化Cookie检查
          guard let cookie = UserDefaults.standard.string(forKey: "free_tv_quark_cookie"),
                !cookie.isEmpty else {
            NSLog("[Quark.macOS] 错误：未设置夸克Cookie")
            result(FlutterError(code: "401", message: "未设置夸克Cookie，请先在设置中登录", details: nil))
            return
          }
          NSLog("[Quark.macOS] Cookie验证通过: \(cookie.prefix(50))...")
          
          do {
            // 只走"转存到个人盘"的路径
            NSLog("[Quark.macOS] 开始验证登录状态")
            let loginValid = validateLogin()
            if !loginValid {
              NSLog("[Quark.macOS] 登录验证失败")
              result(FlutterError(code: "401", message: "夸克登录已过期，请重新登录", details: nil))
              return
            }
            NSLog("[Quark.macOS] 登录验证成功")
            let selfFid = try ensureSavedAndGetSelfFid(shareId: shareId, stoken: stoken, srcFid: fid, srcToken: fileToken)
            var url = ""
            // 策略调整：对于超大文件，优先转码播放（更适合流媒体）
            NSLog("[Quark.macOS] 开始获取播放地址，selfFid: \(selfFid)")
            do {
              // 先尝试转码播放（对于超大文件更稳定）
              NSLog("[Quark.macOS] 尝试转码播放（优先策略）")
              url = try selfTranscodeURL(fid: selfFid, flag: flag)
              NSLog("[Quark.macOS] 转码播放成功")
            } catch {
              NSLog("[Quark.macOS] 转码播放失败: \(error)，尝试直链下载")
              do {
                // 备用：直链下载
                url = try selfDownloadURL(fid: selfFid)
                NSLog("[Quark.macOS] 直链下载成功")
              } catch {
                NSLog("[Quark.macOS] 直链下载也失败: \(error)，抛出错误")
                throw error
              }
            }
            // 强化清洗：去掉尾部垃圾字符、CR/LF，再做空格编码
            func strongerSanitize(_ raw: String) -> String {
              var s = raw.trimmingCharacters(in: .whitespacesAndNewlines)
              
              // 处理URL编码异常：寻找第一个异常字符位置
              if let spaceRange = s.rangeOfCharacter(from: .whitespaces) {
                let beforeSpace = String(s[..<spaceRange.lowerBound])
                // 检查空格前的部分是否是有效的URL结尾
                if beforeSpace.contains("?") || beforeSpace.contains("&") {
                  s = beforeSpace
                }
              }
              
              // 寻找URL参数中的异常编码，截取到第一个异常位置
              if let abnormalRange = s.range(of: " ") {
                let beforeAbnormal = String(s[..<abnormalRange.lowerBound])
                // 确保截取位置合理（包含基本URL结构）
                if beforeAbnormal.count > 100 && (beforeAbnormal.contains("quark.cn") || beforeAbnormal.contains("pds.")) {
                  s = beforeAbnormal
                  NSLog("[Quark.macOS] URL清理：检测到异常编码，截取到: \(s.suffix(50))")
                }
              }
              
              // 移除所有换行符和回车符
              s = s.replacingOccurrences(of: "\r", with: "").replacingOccurrences(of: "\n", with: "")
              
              // 最小化空格处理：仅替换剩余的空格为%20
              return s.replacingOccurrences(of: " ", with: "%20")
            }
            let finalUrl = strongerSanitize(url)
            
            NSLog("[Quark.macOS] URL清理前: \(url.prefix(200))...")
            NSLog("[Quark.macOS] URL清理后: \(finalUrl.prefix(200))...")
            NSLog("[Quark.macOS] URL长度变化: \(url.count) -> \(finalUrl.count)")
            
            // 构建播放所需的头部信息
            var playHeaders: [String: String] = [
              "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/2.5.20 Chrome/100.0.4896.160 Electron/********-b478491100 Safari/537.36 Channel/pckk_other_ch",
              "Referer": "https://pan.quark.cn/",
              "Accept": "*/*",
              "Accept-Language": "zh-CN,zh;q=0.9",
              "Accept-Encoding": "gzip, deflate, br",
              // 添加浏览器指纹头部增强认证
              "Sec-Ch-Ua": "\"Chromium\";v=\"100\", \"Not(A:Brand\";v=\"8\", \"Google Chrome\";v=\"100\"",
              "Sec-Ch-Ua-Mobile": "?0",
              "Sec-Ch-Ua-Platform": "\"Windows\"",
              "Sec-Fetch-Dest": "video",
              "Sec-Fetch-Mode": "cors",
              "Sec-Fetch-Site": "cross-site"
            ]
            
            // 关键修复：为夸克CDN链接添加Cookie认证
            if finalUrl.lowercased().contains("pds.quark.cn") || finalUrl.lowercased().contains("video-play") {
              NSLog("[Quark.macOS] 检测到夸克CDN链接，添加Cookie认证")
              playHeaders["Cookie"] = cookie
              playHeaders["Origin"] = "https://pan.quark.cn/"
            }
            
            // 若链接是 m3u8，补充更精确的 Accept 与 Origin
            let lower = finalUrl.lowercased()
            if lower.hasSuffix(".m3u8") || lower.contains(".m3u8?") {
              playHeaders["Accept"] = "application/x-mpegURL,application/vnd.apple.mpegurl,*/*"
              playHeaders["Origin"] = "https://pan.quark.cn/"
            }
            
            // 使用代理服务器包装URL，确保头部正确传递
            let proxyUrl = ProxyServer.shared.buildProxyUrl(finalUrl, headers: playHeaders)
            
            NSLog("[Quark.macOS] 原始URL: \(finalUrl)")
            NSLog("[Quark.macOS] 代理URL: \(proxyUrl.prefix(160))...")
            
            DispatchQueue.main.async { 
              result([
                "url": proxyUrl, 
                "header": [:], // 清空头部，因为代理服务器会处理
                "parse": "0"
              ]) 
            }
          } catch {
            NSLog("[Quark.macOS] playerContent 执行失败: \(error)")
            DispatchQueue.main.async { 
              // 增强错误信息
              let nsError = error as NSError
              if nsError.domain == "http" && nsError.code == 401 {
                result(FlutterError(code: "401", message: "夸克登录已过期，请重新设置Cookie", details: nil))
              } else if nsError.domain == "http" && nsError.code == 403 {
                result(FlutterError(code: "403", message: "访问被拒绝，可能是权限不足或Cookie无效", details: nil))
              } else {
                result(FlutterError(code: "500", message: "获取播放地址失败: \(error.localizedDescription)", details: nil))
              }
            }
          }
        }

      default:
        result(FlutterMethodNotImplemented)
      }
    }

    super.awakeFromNib()
  }
  
  deinit {
    // 停止代理服务器
    ProxyServer.shared.stop()
  }
}

// 流式代理处理器 - 用于处理大文件的实时流传输
class StreamingProxyHandler: NSObject, URLSessionDataDelegate {
  private let originalUrl: String
  private var headers: [String: String]
  private let connection: NWConnection
  private let playerInfo: (name: String, isLowLatency: Bool, preferredChunkSize: Int)
  private var session: URLSession?
  private var headersSent = false
  private var contentLength: Int64 = 0
  private var receivedBytes: Int64 = 0
  
  // 上游失败回退标记
  private var attemptedDefaultRange = false
  private var attemptedRemoveRange = false
  private var attemptedRemoveOrigin = false
  
  // 智能缓冲配置 - 根据播放器类型调整
  private var initialBufferSize: Int64
  private var chunkBufferSize: Int64
  private var maxBufferSize: Int64
  private var isInitialBuffering = true
  private var bufferQueue: [Data] = []
  private var bufferedBytes: Int64 = 0
  private var playbackStarted = false
  private var shouldPause = false
  
  // M3U8 内容处理
  private var allData = Data()
  private var httpResponse: HTTPURLResponse?
  private var m3u8ContentSent = false
  
  // 重试机制
  private var retryCount = 0
  private let maxRetries = 3
  
  init(originalUrl: String, headers: [String: String], connection: NWConnection, playerInfo: (name: String, isLowLatency: Bool, preferredChunkSize: Int)) {
    self.originalUrl = originalUrl
    self.headers = headers
    self.connection = connection
    self.playerInfo = playerInfo
    
    // 根据播放器类型调整缓冲策略
    if playerInfo.isLowLatency {
      // 低延迟播放器使用较小的缓冲
      self.initialBufferSize = Int64(playerInfo.preferredChunkSize)
      self.chunkBufferSize = Int64(playerInfo.preferredChunkSize / 2)
      self.maxBufferSize = Int64(playerInfo.preferredChunkSize * 5)
    } else {
      // 普通播放器使用较大的缓冲以提高稳定性
      self.initialBufferSize = Int64(playerInfo.preferredChunkSize * 2)
      self.chunkBufferSize = Int64(playerInfo.preferredChunkSize)
      self.maxBufferSize = Int64(playerInfo.preferredChunkSize * 10)
    }
    
    super.init()
    
    NSLog("[StreamingProxy] 为\(playerInfo.name)优化: 初始缓冲=\(initialBufferSize/1024)KB, 块大小=\(chunkBufferSize/1024)KB, 低延迟=\(playerInfo.isLowLatency)")
  }
  
  func startStreaming() {
    NSLog("[StreamingProxy] 开始智能流式传输: \(originalUrl.prefix(100))...")

    guard let url = URL(string: originalUrl) else {
      sendErrorResponse(status: 400, message: "Invalid URL")
      return
    }
    
    var request = URLRequest(url: url)
    for (key, value) in headers {
      request.setValue(value, forHTTPHeaderField: key)
    }
    
    // 诊断性日志：打印完整请求头信息与URL结构
    if let allHeaders = request.allHTTPHeaderFields {
      NSLog("[StreamingProxy] 请求头数量: \(allHeaders.count)")
      NSLog("[StreamingProxy] 请求URL Host: \(url.host ?? "<nil>") Path: \(url.path)")
      let hasWhitespace = originalUrl.rangeOfCharacter(from: .whitespacesAndNewlines) != nil
      if hasWhitespace {
        NSLog("[StreamingProxy] 警告：URL 含有空白/换行字符")
        NSLog("[StreamingProxy] URL尾部(最多20字符)：\(originalUrl.suffix(20))")
      }
      for (key, value) in allHeaders {
        if key.lowercased() == "cookie" {
          NSLog("[StreamingProxy] \(key): <cookie length=\(value.count)>")
        } else {
          NSLog("[StreamingProxy] \(key): \(value)")
        }
      }
    }
    
    let config = URLSessionConfiguration.default
    config.timeoutIntervalForRequest = 60 // 延长超时以支持初始缓冲
    config.timeoutIntervalForResource = 0 // 无总超时限制
    config.requestCachePolicy = .reloadIgnoringLocalAndRemoteCacheData // 确保不使用缓存
    config.urlCache = nil // 禁用缓存
    config.allowsCellularAccess = true
    config.httpShouldUsePipelining = false
    config.waitsForConnectivity = true // 等待网络连接
    
    session = URLSession(configuration: config, delegate: self, delegateQueue: nil)
    let task = session!.dataTask(with: request)
    task.resume()
    
    NSLog("[StreamingProxy] 启动智能缓冲任务，ID: \(task.taskIdentifier)")
  }
  
  // MARK: - URLSessionDataDelegate
  
  func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive response: URLResponse, completionHandler: @escaping (URLSession.ResponseDisposition) -> Void) {
    NSLog("[StreamingProxy] 收到响应头，开始智能缓冲")
    
    guard let httpResponse = response as? HTTPURLResponse else {
      sendErrorResponse(status: 500, message: "Invalid Response")
      completionHandler(.cancel)
      return
    }
    
    // 保存响应对象供后续使用
    self.httpResponse = httpResponse
    
    NSLog("[StreamingProxy] HTTP状态码: \(httpResponse.statusCode)")
    
    // 检查状态码，必要时进行一次智能回退重试
    if httpResponse.statusCode < 200 || httpResponse.statusCode >= 400 {
      NSLog("[StreamingProxy] HTTP错误状态: \(httpResponse.statusCode)")
      // 诊断性日志：打印上游响应头
      let upstreamHeaders = httpResponse.allHeaderFields
      NSLog("[StreamingProxy] 上游响应头字段数: \(upstreamHeaders.count)")
      for (k, v) in upstreamHeaders {
        NSLog("[StreamingProxy] < \(k): \(v)")
      }
      let status = httpResponse.statusCode
      // 提取当前请求关键头部
      let currentHasRange = headers["Range"] != nil
      let currentHasOrigin = headers["Origin"] != nil
      
      // 412 优先：移除 Range 重试一次
      if status == 412 && currentHasRange && !attemptedRemoveRange {
        attemptedRemoveRange = true
        var newHeaders = headers
        newHeaders.removeValue(forKey: "Range")
        NSLog("[StreamingProxy] 上游 412，移除 Range 重试一次")
        restartStreaming(with: newHeaders)
        completionHandler(.cancel)
        return
      }
      // 400/416：优先尝试移除 Range 重试一次
      if (status == 400 || status == 416) && currentHasRange && !attemptedRemoveRange {
        attemptedRemoveRange = true
        var newHeaders = headers
        newHeaders.removeValue(forKey: "Range")
        NSLog("[StreamingProxy] 上游 \(status)，移除 Range 重试一次")
        restartStreaming(with: newHeaders)
        completionHandler(.cancel)
        return
      }
      
      // 400：尝试移除 Origin（保留 Referer）重试一次
      if status == 400 && currentHasOrigin && !attemptedRemoveOrigin {
        attemptedRemoveOrigin = true
        var newHeaders = headers
        newHeaders.removeValue(forKey: "Origin")
        NSLog("[StreamingProxy] 上游 400，移除 Origin 重试一次（保留 Referer）")
        restartStreaming(with: newHeaders)
        completionHandler(.cancel)
        return
      }
      
      // 400：若无 Range，则添加默认 Range 重试一次（放在移除 Range 和移除 Origin 之后）
      if status == 400 && !currentHasRange && !attemptedDefaultRange {
        attemptedDefaultRange = true
        var newHeaders = headers
        newHeaders["Range"] = "bytes=0-"
        NSLog("[StreamingProxy] 上游 400，添加默认 Range 重试一次（兜底）")
        restartStreaming(with: newHeaders)
        completionHandler(.cancel)
        return
      }
      
      // 无可回退或已回退失败
      sendErrorResponse(status: status, message: "HTTP Error \(status)")
      completionHandler(.cancel)
      return
    }
    
    // 获取内容长度
    if let contentLengthStr = httpResponse.allHeaderFields["Content-Length"] as? String,
       let length = Int64(contentLengthStr) {
      contentLength = length
      NSLog("[StreamingProxy] 内容长度: \(contentLength) 字节 (\(contentLength/1024/1024)MB)")
    }
    
    // 检查是否是 M3U8 文件
    let isM3U8 = originalUrl.lowercased().hasSuffix(".m3u8") || 
                 originalUrl.lowercased().contains(".m3u8?") ||
                 (httpResponse.allHeaderFields["Content-Type"] as? String)?.contains("application/vnd.apple.mpegurl") == true ||
                 (httpResponse.allHeaderFields["Content-Type"] as? String)?.contains("application/x-mpegURL") == true
    
    if isM3U8 {
      NSLog("[StreamingProxy] 检测到 M3U8 文件，立即发送响应头避免播放器超时")
      // 关键修复：对于M3U8文件，立即发送响应头，避免播放器等待超时
      sendResponseHeaders(httpResponse)
      headersSent = true
      
      // M3U8 文件需要收集完整内容后再处理URL转换
      isInitialBuffering = false
      playbackStarted = false
    } else {
      // 非 M3U8：无论 200 还是 206，均立即开始边下边播，避免长时间"加载中"
      NSLog("[StreamingProxy] 非M3U8媒体，直接开始流式播放（无初始缓冲）")
      isInitialBuffering = false
      playbackStarted = true
      
      // 立即发送响应头
      sendResponseHeaders(httpResponse)
      headersSent = true
    }
    
    completionHandler(.allow)
  }

  // 使用新的头部重启上游流式请求
  private func restartStreaming(with newHeaders: [String: String]) {
    session?.invalidateAndCancel()
    self.headers = newHeaders
    NSLog("[StreamingProxy] 使用回退策略重启请求。Range=\(newHeaders["Range"] ?? "<none>") Origin=\(newHeaders["Origin"] != nil)")
    startStreaming()
  }
  
  func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive data: Data) {
    receivedBytes += Int64(data.count)
    
    // 总是收集所有数据，以便处理M3U8
    allData.append(data)
    
    let progress = contentLength > 0 ? Double(receivedBytes) / Double(contentLength) * 100 : 0
    
    // 检查是否是 M3U8 文件
    let isM3U8 = originalUrl.lowercased().hasSuffix(".m3u8") || 
                 originalUrl.lowercased().contains(".m3u8?")
    
    if isM3U8 {
      if Int(progress) % 10 == 0 || receivedBytes == contentLength {
        NSLog("[StreamingProxy] M3U8 数据收集: \(receivedBytes) 字节, 进度: \(String(format: "%.1f", progress))%")
      }
      
      // 当M3U8收集完成时，分析内容并重写TS URL
      if receivedBytes == contentLength {
        NSLog("[StreamingProxy] 🔍 M3U8收集完成，分析内容...")
        if let dataString = String(data: allData, encoding: .utf8) {
          let lines = dataString.components(separatedBy: CharacterSet.newlines)
          NSLog("[StreamingProxy] M3U8总行数: \(lines.count)")
          
          // 使用与主代理一致的方法重写M3U8内容为 /proxy?key=...，确保服务器可识别
          let modifiedContent = self.modifyM3U8Content(dataString, baseUrl: self.originalUrl, headers: self.headers)
          if let modifiedData = modifiedContent.data(using: .utf8) {
            allData = modifiedData
            let tsCount = modifiedContent
              .components(separatedBy: "\n")
              .filter { !$0.hasPrefix("#") && $0.contains("/proxy?key=") }
              .count
            NSLog("[StreamingProxy] ✅ M3U8内容重写完成，TS片段数: \(tsCount)")
            // 立刻发送 M3U8 正文，并通知主代理释放等待队列
            if !m3u8ContentSent {
              m3u8ContentSent = true
              NSLog("[StreamingProxy] 立即发送已重写的 M3U8 正文，并发布给等待连接")
              // 先在主代理中缓存并释放等待队列
              ProxyServer.shared.publishM3U8Content(originalUrl: self.originalUrl, content: modifiedContent)
              // 然后将当前连接的响应也发送出去
              processM3U8Content()
            }
          }
        }
      }
      
      // M3U8 文件等待完整内容收集完成
      return
    }
    
    // 如果客户端连接已失败或未就绪，停止上游拉流，避免无意义缓冲
    if connection.state != .ready {
      NSLog("[StreamingProxy] 客户端连接已失效(\(connection.state))，停止上游传输并中止当前请求")
      session.invalidateAndCancel()
      return
    }
    
    // 如果是 Range 请求或者已经开始播放，根据播放器特性调整传输
    if playbackStarted || !isInitialBuffering {
      // 根据播放器特性调整数据块大小
      let adaptedData = adaptDataForPlayer(data)
      NSLog("[StreamingProxy] 为\(playerInfo.name)调整数据传输: \(adaptedData.count) 字节, 总进度: \(String(format: "%.1f", progress))%")
      sendDataChunk(adaptedData)
      return
    }
    
    // 初始缓冲阶段
    bufferedBytes += Int64(data.count)
    bufferQueue.append(data)
    
    if bufferedBytes % (256*1024) == 0 || bufferedBytes >= initialBufferSize {
      NSLog("[StreamingProxy] 初始缓冲(\(playerInfo.name)): \(bufferedBytes/1024)KB / \(initialBufferSize/1024)KB (\(String(format: "%.1f", Double(bufferedBytes)/Double(initialBufferSize)*100))%)")
    }
    
    // 根据播放器类型调整缓冲完成条件
    let bufferThreshold = playerInfo.isLowLatency ? initialBufferSize / 2 : initialBufferSize
    
    // 达到缓冲阈值，开始播放
    if bufferedBytes >= bufferThreshold || (contentLength > 0 && receivedBytes >= contentLength) {
      NSLog("[StreamingProxy] 🎬 初始缓冲完成(\(playerInfo.name))，开始播放")
      isInitialBuffering = false
      playbackStarted = true
      flushBufferForPlayer()
    }
  }
  
  // 根据播放器特性调整数据块
  private func adaptDataForPlayer(_ data: Data) -> Data {
    // 如果数据块太大，分割成播放器偏好的大小
    if data.count > playerInfo.preferredChunkSize {
      // 返回第一个偏好大小的块，剩余部分缓存
      let preferredData = data.prefix(playerInfo.preferredChunkSize)
      let remainingData = data.dropFirst(playerInfo.preferredChunkSize)
      
      if !remainingData.isEmpty {
        bufferQueue.append(Data(remainingData))
        bufferedBytes += Int64(remainingData.count)
      }
      
      return Data(preferredData)
    }
    
    return data
  }
  
  // 根据播放器优化的缓冲刷新
  private func flushBufferForPlayer() {
    if bufferQueue.count % 5 == 0 {
      NSLog("[StreamingProxy] 💾 为\(playerInfo.name)优化缓冲刷新: \(bufferQueue.count)个块，总计\(bufferedBytes/1024)KB")
    }
    
    if playerInfo.isLowLatency {
      // 低延迟播放器：快速刷新所有缓冲
      for data in bufferQueue {
        sendDataChunk(data)
      }
    } else {
      // 普通播放器：合并小块后发送
      var mergedData = Data()
      for data in bufferQueue {
        mergedData.append(data)
        
        // 当合并的数据达到偏好大小时发送
        if mergedData.count >= playerInfo.preferredChunkSize {
          sendDataChunk(mergedData)
          mergedData = Data()
        }
      }
      
      // 发送剩余数据
      if !mergedData.isEmpty {
        sendDataChunk(mergedData)
      }
    }
    
    bufferQueue.removeAll()
    bufferedBytes = 0
  }
  
  func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didCompleteWithError error: Error?) {
    NSLog("[StreamingProxy] didCompleteWithError called, error=\(error?.localizedDescription ?? "nil")")
    
    if let error = error {
      let nsError = error as NSError
      
      // 检查是否应该重试
      if retryCount < maxRetries && 
         (nsError.code == NSURLErrorTimedOut || 
          nsError.code == NSURLErrorNetworkConnectionLost ||
          nsError.code == NSURLErrorNotConnectedToInternet) {
        retryCount += 1
        NSLog("[StreamingProxy] 传输错误，重试 \(retryCount)/\(maxRetries): \(error)")
        
        // 重置状态
        headersSent = false
        receivedBytes = 0
        allData = Data()
        bufferQueue.removeAll()
        bufferedBytes = 0
        isInitialBuffering = true
        playbackStarted = false
        
        // 延迟重试
        DispatchQueue.global().asyncAfter(deadline: .now() + 1.0) {
          if let url = URL(string: self.originalUrl) {
            var request = URLRequest(url: url)
            for (key, value) in self.headers {
              request.setValue(value, forHTTPHeaderField: key)
            }
            self.startStreaming()
          }
        }
        return
      }
      
      NSLog("[StreamingProxy] 传输完成，有错误（达到最大重试次数）: \(error)")
      if !headersSent {
        if nsError.code == NSURLErrorTimedOut {
          sendErrorResponse(status: 408, message: "Request Timeout")
        } else if nsError.code == NSURLErrorNetworkConnectionLost {
          sendErrorResponse(status: 499, message: "Client Closed Connection")
        } else {
          sendErrorResponse(status: 500, message: "Streaming Error: \(error.localizedDescription)")
        }
      }
    } else {
      NSLog("[StreamingProxy] 🎉 传输成功完成，总计: \(receivedBytes/1024/1024)MB")
      
      // 检查是否是 M3U8 文件
      let isM3U8 = originalUrl.lowercased().hasSuffix(".m3u8") || 
                   originalUrl.lowercased().contains(".m3u8?")
      
      NSLog("[StreamingProxy] isM3U8=\(isM3U8), headersSent=\(headersSent), allData.count=\(allData.count)")
      
      if isM3U8 {
        // 始终处理并发送 M3U8 内容（无论是否已发送过响应头）
        if !m3u8ContentSent {
          NSLog("[StreamingProxy] 调用 processM3U8Content() 发送 M3U8 内容（来自完成回调）")
          m3u8ContentSent = true
          processM3U8Content()
        } else {
          NSLog("[StreamingProxy] M3U8 内容已发送，跳过重复发送")
        }
        // processM3U8Content 内部负责在发送完成后关闭连接
        return
      } else {
        NSLog("[StreamingProxy] 发送剩余缓冲数据（非M3U8）")
        // 发送剩余缓冲数据
        if !bufferQueue.isEmpty {
          flushBufferForPlayer()
        }
      }
    }
    
    // 关闭连接（非 M3U8 情况在这里统一关闭）
    NSLog("[StreamingProxy] 关闭连接")
    connection.cancel()
    session.invalidateAndCancel()
  }
  
  // 处理 M3U8 内容
  private func processM3U8Content() {
    NSLog("[StreamingProxy] processM3U8Content 开始执行")
    
    guard let _ = self.httpResponse else {
      NSLog("[StreamingProxy] M3U8 内容解析失败: httpResponse=\(self.httpResponse != nil), allData.count=\(allData.count)")
      sendErrorResponse(status: 500, message: "M3U8 Content Parse Error")
      return
    }
    
    NSLog("[StreamingProxy] 开始处理 M3U8 内容，大小: \(allData.count) 字节")
    
    // allData 已经在 didReceive data 中被重写过了，直接使用
    guard let modifiedContent = String(data: allData, encoding: .utf8) else {
      NSLog("[StreamingProxy] M3U8 内容编码失败")
      sendErrorResponse(status: 500, message: "M3U8 Content Encoding Error")
      return
    }
    
    NSLog("[StreamingProxy] M3U8内容已重写，行数: \(modifiedContent.components(separatedBy: "\n").count)")
    
    // 根据是否已发送过响应头决定发送策略
    if let modifiedData = modifiedContent.data(using: .utf8) {
      NSLog("[StreamingProxy] M3U8 处理完成，修改后大小: \(modifiedData.count) 字节，开始发送数据")
      if headersSent {
        // 仅发送正文并在完成后关闭连接
        connection.send(content: modifiedData, completion: .contentProcessed { [weak self] error in
          if let error = error { 
            NSLog("[StreamingProxy] 发送M3U8正文失败: \(error)") 
          } else {
            NSLog("[StreamingProxy] 发送M3U8正文成功")
          }
          NSLog("[StreamingProxy] 发送M3U8正文完成，延迟关闭连接")
          // 延迟关闭连接，确保数据完全传输到播放器
          DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self?.connection.cancel()
            self?.session?.invalidateAndCancel()
          }
        })
      } else {
        // 组装完整响应（带 Content-Length）后一次性发送
        var headersStr = "HTTP/1.1 200 OK\r\n"
        headersStr += "Content-Type: application/x-mpegURL\r\n"
        headersStr += "Content-Length: \(modifiedData.count)\r\n"
        headersStr += "Cache-Control: no-cache, no-store, must-revalidate\r\n"
        headersStr += "Pragma: no-cache\r\n"
        headersStr += "Expires: 0\r\n"
        headersStr += "Access-Control-Allow-Origin: *\r\n"
        headersStr += "Access-Control-Allow-Methods: GET, POST, OPTIONS\r\n"
        headersStr += "Access-Control-Allow-Headers: *\r\n"
        headersStr += "Connection: close\r\n\r\n"
        var finalData = headersStr.data(using: .utf8) ?? Data()
        finalData.append(modifiedData)
        connection.send(content: finalData, completion: .contentProcessed { [weak self] error in
          if let error = error { 
            NSLog("[StreamingProxy] 发送M3U8完整响应失败: \(error)") 
          } else {
            NSLog("[StreamingProxy] 发送M3U8完整响应成功")
          }
          NSLog("[StreamingProxy] 发送M3U8完整响应完成，延迟关闭连接")
          // 延迟关闭连接，确保数据完全传输到播放器
          DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self?.connection.cancel()
            self?.session?.invalidateAndCancel()
          }
        })
      }
    } else {
      NSLog("[StreamingProxy] M3U8 内容编码失败")
      sendErrorResponse(status: 500, message: "M3U8 Content Encoding Error")
      // 出错也应关闭连接
      connection.cancel()
      session?.invalidateAndCancel()
    }
    
    NSLog("[StreamingProxy] processM3U8Content 执行完成（发送流程已安排）")
  }
  
  // M3U8 内容修改方法（复用主代理服务器的实现）
  private func modifyM3U8Content(_ content: String, baseUrl: String, headers: [String: String]) -> String {
    // 使用通用换行符分割，避免遗留 \r 导致 URL 携带回车字符
    let lines = content.components(separatedBy: .newlines)
    var modifiedLines: [String] = []

    guard URL(string: baseUrl) != nil else {
      return content
    }

    for line in lines {
      // 去除行两端的空白和换行，避免 TS URL 含有不可见字符（如 \r）
      var trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
      // 统一净化：删除第一个空白(Unicode)及其后的所有内容（涵盖全角/零宽等情况）
      let originalForLog = trimmedLine
      if !trimmedLine.hasPrefix("#") && !trimmedLine.isEmpty {
        NSLog("[ProxyServer] 🧹 正在进行TS行净化")
        let sanitized = trimmedLine.replacingOccurrences(of: "\\p{Z}+.*$", with: "", options: [.regularExpression])
        if sanitized != trimmedLine {
          NSLog("[StreamingProxy] 🧹 TS行净化: 原='\(originalForLog)' 净='\(sanitized)'")
          trimmedLine = sanitized
        }
      }

      // 如果是 TS 文件行（不以 # 开头且包含 .ts）
      if !trimmedLine.hasPrefix("#") && !trimmedLine.isEmpty {
        if trimmedLine.hasSuffix(".ts") || trimmedLine.contains(".ts?") {
          // 构建完整的 TS URL
          let tsUrl: String
          if trimmedLine.hasPrefix("http") {
            // 已经是完整 URL
            tsUrl = trimmedLine
          } else {
            // 相对路径，需要拼接
            let baseUrlWithoutFile = baseUrl.components(separatedBy: "/").dropLast().joined(separator: "/")
            tsUrl = "\(baseUrlWithoutFile)/\(trimmedLine)"
          }

          // 为 TS URL 创建代理映射并生成代理 URL
          // 注意：TS 段需要保留 Cookie 用于夸克 CDN 认证
          var tsHeaders = headers
          let lowercasedTsUrl = tsUrl.lowercased()
          // 更全面地识别夸克CDN域名，确保TS片段保留Cookie进行鉴权
          let isQuarkCDN =
            lowercasedTsUrl.contains("pds.quark.cn") ||
            lowercasedTsUrl.contains("video-play") ||
            lowercasedTsUrl.contains("drive.quark.cn") ||
            lowercasedTsUrl.contains("video-h-kdac") ||
            lowercasedTsUrl.contains("quark.cn")

          if !isQuarkCDN {
            // 只有非夸克CDN才移除Cookie
            tsHeaders.removeValue(forKey: "Cookie")
          } else {
            // 夸克CDN增加必要头部
            tsHeaders["Origin"] = "https://pan.quark.cn/"
          }

          let proxyUrl = ProxyServer.shared.buildProxyUrl(tsUrl, headers: tsHeaders)
          modifiedLines.append(proxyUrl)
          NSLog("[StreamingProxy] 转换 TS URL: 原='\(originalForLog)' 净='\(trimmedLine)' -> 代理='\(proxyUrl)'")
          let bytes = trimmedLine.data(using: .utf8)?.map { String(format: "%02X", $0) }.joined(separator: " ")
          NSLog("字符串十六进制: \(bytes ?? "nil")")
        } else {
          // 其他类型的资源文件
          modifiedLines.append(line)
        }
      } else {
        // 注释行或其他内容保持原样
        modifiedLines.append(line)
      }
    }
    let result = modifiedLines.joined(separator: "\n")
    NSLog("[ProxyServer] M3U8内容处理完成：原始行数=\(lines.count)，处理后行数=\(modifiedLines.count)")

    // 调试：输出前几行M3U8内容
    let previewLines = modifiedLines.prefix(10)
    NSLog("[ProxyServer] M3U8内容预览：")
    for (index, line) in previewLines.enumerated() {
      NSLog("[ProxyServer]   第\(index+1)行: \(line)")
    }

    // 调试：输出包含proxy的行
    let proxyLines = modifiedLines.filter { $0.contains("/proxy?key=") }
    NSLog("[ProxyServer] 代理URL行数: \(proxyLines.count)")
    if let firstProxyLine = proxyLines.first {
      NSLog("[ProxyServer] 第一个代理URL: \(firstProxyLine)")
    }

    return result
  }
  
  // 发送缓冲队列中的所有数据 - 已被 flushBufferForPlayer 替代
  
  // 发送单个数据块 - 增强连接状态检查和错误处理
  private func sendDataChunk(_ data: Data) {
    // 检查连接状态
    guard connection.state == .ready else {
      NSLog("[StreamingProxy] 连接未就绪，终止上游会话并跳过数据发送: \(connection.state)")
      session?.invalidateAndCancel()
      return
    }
    
    // 播放器兼容性检查 - 检测播放器类型并调整发送策略
    let isSmallChunk = data.count < 1024 * 1024 // 小于1MB的数据块
    let sendCompletion: NWConnection.SendCompletion
    
    if isSmallChunk {
      // 对小数据块使用contentProcessed，确保可靠传输
      sendCompletion = .contentProcessed { [weak self] error in
        self?.handleSendResult(error: error, dataSize: data.count, isSmallChunk: true)
      }
    } else {
      // 对大数据块使用contentProcessed，避免阻塞
      sendCompletion = .contentProcessed { [weak self] error in
        self?.handleSendResult(error: error, dataSize: data.count, isSmallChunk: false)
      }
    }
    
    connection.send(content: data, completion: sendCompletion)
  }
  
  // 统一处理发送结果
  private func handleSendResult(error: Error?, dataSize: Int, isSmallChunk: Bool) {
    if let error = error {
      let nsError = error as NSError
      let chunkType = isSmallChunk ? "小块" : "大块"
      
      switch nsError.code {
      case 32: // Broken pipe
        NSLog("[StreamingProxy] \(chunkType)数据发送失败: \(error) - 播放器已断开连接")
        // 播放器断开连接，停止传输但不重试
        session?.invalidateAndCancel()
      case 54: // Connection reset by peer
        NSLog("[StreamingProxy] \(chunkType)数据发送失败: \(error) - 连接被重置")
        // 连接重置，可能是播放器切换或网络问题
        if connection.state != .ready {
          session?.invalidateAndCancel()
        }
      case 60: // Operation timed out
        NSLog("[StreamingProxy] \(chunkType)数据发送超时: \(error) - 播放器响应慢")
        // 播放器响应慢，但继续尝试
      default:
        NSLog("[StreamingProxy] \(chunkType)数据发送失败: \(error)")
      }
      
      // 检查连接状态，如果已断开则停止传输
      if connection.state != .ready {
        NSLog("[StreamingProxy] 连接已断开，停止数据传输")
        session?.invalidateAndCancel()
      }
    } else {
      let sizeStr = dataSize > 1024 * 1024 ? "\(dataSize/1024/1024)MB" : "\(dataSize/1024)KB"
      NSLog("[StreamingProxy] 数据块发送成功: \(sizeStr)")
    }
  }
  
  private func sendResponseHeaders(_ response: HTTPURLResponse) {
    // 工具：大小写不敏感获取头部值
    func headerValue(_ name: String, from headers: [AnyHashable: Any]) -> String? {
      for (k, v) in headers {
        if String(describing: k).lowercased() == name.lowercased() {
          return String(describing: v)
        }
      }
      return nil
    }

    var responseHeaders = "HTTP/1.1 \(response.statusCode) \(HTTPURLResponse.localizedString(forStatusCode: response.statusCode))\r\n"

    // 复制关键头：Content-Range / Accept-Ranges（大小写不敏感）
    if let cr = headerValue("Content-Range", from: response.allHeaderFields) {
      responseHeaders += "Content-Range: \(cr)\r\n"
      NSLog("[StreamingProxy] 透传 Content-Range: \(cr)")
    }
    let arValue = headerValue("Accept-Ranges", from: response.allHeaderFields) ?? "bytes"
    responseHeaders += "Accept-Ranges: \(arValue)\r\n"

    // 是否 M3U8
    let ctLower = headerValue("Content-Type", from: response.allHeaderFields)?.lowercased() ?? ""
    let isM3U8 = originalUrl.lowercased().hasSuffix(".m3u8") ||
                 originalUrl.lowercased().contains(".m3u8?") ||
                 ctLower.contains("application/vnd.apple.mpegurl") ||
                 ctLower.contains("application/x-mpegurl")

    if isM3U8 {
      NSLog("[StreamingProxy] 检测到 M3U8 文件，设置专用响应头")
      responseHeaders += "Content-Type: application/x-mpegURL\r\n"
      responseHeaders += "Cache-Control: no-cache, no-store, must-revalidate\r\n"
      responseHeaders += "Pragma: no-cache\r\n"
      responseHeaders += "Expires: 0\r\n"
      responseHeaders += "Connection: close\r\n"
      // 不设置 Content-Length；不宣称 chunked，后续我们直接发送完整修改后的内容
    } else {
      // 普通文件：保留 Content-Type
      if let ct = headerValue("Content-Type", from: response.allHeaderFields) {
        responseHeaders += "Content-Type: \(ct)\r\n"
      }
      // Content-Length：优先透传；若缺失而有 Content-Range，则计算区间长度补齐
      var contentLengthStr = headerValue("Content-Length", from: response.allHeaderFields)
      if contentLengthStr == nil, let cr = headerValue("Content-Range", from: response.allHeaderFields) {
        // 解析 bytes start-end/total
        let parts = cr.replacingOccurrences(of: "bytes ", with: "").split(separator: "/")
        if parts.count == 2, let range = parts.first?.split(separator: "-") {
          if range.count == 2, let s = Int64(range[0]), let e = Int64(range[1]), e >= s {
            contentLengthStr = String(e - s + 1)
            NSLog("[StreamingProxy] 依据 Content-Range 计算 Content-Length=\(contentLengthStr!)")
          }
        }
      }
      if let cl = contentLengthStr {
        responseHeaders += "Content-Length: \(cl)\r\n"
      }
      responseHeaders += "Cache-Control: no-cache\r\n"
    }

    // CORS 头部
    responseHeaders += "Access-Control-Allow-Origin: *\r\n"
    responseHeaders += "Access-Control-Allow-Methods: GET, POST, OPTIONS\r\n"
    responseHeaders += "Access-Control-Allow-Headers: *\r\n"

    responseHeaders += "\r\n"

    NSLog("[StreamingProxy] 发送智能流式响应头: HTTP \(response.statusCode)")

    if let headerData = responseHeaders.data(using: .utf8) {
      connection.send(content: headerData, completion: .contentProcessed { error in
        if let error = error {
          NSLog("[StreamingProxy] 发送响应头失败: \(error)")
        } else {
          NSLog("[StreamingProxy] 响应头发送成功，开始智能缓冲")
        }
      })
    }
  }
  
  private func sendErrorResponse(status: Int, message: String) {
    let response = """
      HTTP/1.1 \(status) \(message)
      Content-Type: text/plain
      Content-Length: \(message.count)
      Access-Control-Allow-Origin: *
      
      \(message)
      """.replacingOccurrences(of: "\n", with: "\r\n")
    
    connection.send(content: response.data(using: .utf8), completion: .contentProcessed { _ in
      self.connection.cancel()
    })
  }
}
