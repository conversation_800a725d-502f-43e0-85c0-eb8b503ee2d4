# 夸克网盘TS文件400错误修复报告

## 问题背景

在播放夸克网盘分享的视频时，遇到了TS (Transport Stream) 文件返回400错误的问题，导致视频无法正常播放。通过深入分析AndroidCatVodSpider-multiThread项目的成功实现，发现了关键的解决方案。

## 根本原因分析

经过对比分析，发现主要问题在于**缺少现代浏览器的指纹头部**。夸克的CDN服务器会检查请求头部来验证是否为合法的浏览器请求，而我们之前的实现缺少了关键的`Sec-Fetch-*`和`Sec-Ch-UA`系列头部，被服务器识别为非浏览器请求而拒绝。

## 关键修复内容

### 1. 添加浏览器指纹头部

为所有夸克CDN请求添加了完整的浏览器指纹头部：

```swift
// 添加关键的浏览器指纹头部，参考AndroidCatVodSpider成功实现
outboundHeaders["Sec-Ch-Ua"] = "\"Chromium\";v=\"100\", \"Not(A:Brand\";v=\"8\", \"Google Chrome\";v=\"100\""
outboundHeaders["Sec-Ch-Ua-Mobile"] = "?0"
outboundHeaders["Sec-Ch-Ua-Platform"] = "\"Windows\""
outboundHeaders["Sec-Fetch-Dest"] = isM3U8File ? "empty" : (isTSFile ? "video" : "empty")
outboundHeaders["Sec-Fetch-Mode"] = "cors"
outboundHeaders["Sec-Fetch-Site"] = "cross-site"
```

### 2. 修正Origin格式

```swift
// 修改前（错误）
outboundHeaders["Origin"] = "https://pan.quark.cn/"

// 修改后（正确）
outboundHeaders["Origin"] = "https://pan.quark.cn"  // 移除尾部斜杠
```

### 3. 优化Accept头部

```swift
// M3U8文件：简化Accept头部
outboundHeaders["Accept"] = "*/*"  // 之前：application/x-mpegURL,application/vnd.apple.mpegurl,*/*

// TS文件：添加合适的Accept头部
if isTSFile {
  outboundHeaders["Accept"] = "*/*"
  outboundHeaders["Accept-Language"] = "zh-CN,zh;q=0.9"
}
```

### 4. 增强Host头部处理

根据AndroidCatVodSpider的成功实现，对夸克CDN明确移除Host和Content-Type头部：

```swift
// 根据AndroidCatVodSpider的成功实现，对夸克CDN明确移除Host和Content-Type头部
if isQuarkCDN && (isTS || isM3U8File) {
  outboundHeaders.removeValue(forKey: "Host")
  outboundHeaders.removeValue(forKey: "Content-Type")
  NSLog("[ProxyServer] 夸克CDN: 移除Host和Content-Type头部")
}
```

### 5. 全面覆盖所有夸克API请求

不仅仅是代理服务器，还为所有夸克API请求（detail、playerContent等）添加了浏览器指纹头部，确保整个认证链路的一致性。

## 技术原理

### 浏览器指纹识别机制

现代Web服务器（特别是CDN）会通过以下头部来识别合法的浏览器请求：

1. **Sec-Fetch-\*** 系列：指示请求的上下文和目的
   - `Sec-Fetch-Dest`: 请求的目标类型（video、empty等）
   - `Sec-Fetch-Mode`: 请求模式（cors、same-origin等）  
   - `Sec-Fetch-Site`: 请求来源（cross-site、same-origin等）

2. **Sec-Ch-UA** 系列：客户端提示头部，提供浏览器信息
   - `Sec-Ch-Ua`: 浏览器品牌和版本
   - `Sec-Ch-Ua-Mobile`: 是否为移动设备
   - `Sec-Ch-Ua-Platform`: 操作系统平台

这些头部是现代浏览器自动添加的，缺少这些头部的请求会被识别为自动化工具或恶意请求。

### AndroidCatVodSpider的成功策略

通过分析AndroidCatVodSpider-multiThread项目的代码，发现其成功的关键在于：

1. **完整的浏览器伪装**：包含了所有必要的现代浏览器头部
2. **动态头部处理**：根据不同的文件类型设置不同的`Sec-Fetch-Dest`
3. **严格的头部清理**：明确移除可能导致冲突的Host和Content-Type头部

## 测试验证

修复后应该观察到以下变化：

1. **TS文件请求成功**：之前返回400的TS文件现在应该返回200/206
2. **完整的请求头部**：控制台日志中应该看到新增的Sec-Fetch-*头部
3. **视频正常播放**：夸克网盘视频应该能够流畅播放

## 后续监控

建议继续监控以下指标：

1. **错误率**：TS文件的400错误应该显著减少
2. **播放成功率**：夸克网盘视频的播放成功率应该提升
3. **兼容性**：确保修改不影响其他源的播放

## 参考资料

- [AndroidCatVodSpider-multiThread/QuarkApi.java](AndroidCatVodSpider-multiThread/app/src/main/java/com/github/catvod/api/QuarkApi.java)
- [AndroidCatVodSpider-multiThread/ProxyServer.kt](AndroidCatVodSpider-multiThread/app/src/main/java/com/github/catvod/utils/ProxyServer.kt)
- [Mozilla Web Security Headers](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Sec-Fetch-Dest)

---

**修复日期**: 2024年12月
**影响范围**: 夸克网盘视频播放
**预期效果**: 解决TS文件400错误，提升播放成功率
