#!/usr/bin/env dart

// 测试Dio修复
// 运行命令: dart run test_dio_fix.dart

import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';

void main() async {
  print('🧪 测试Dio gzip处理修复');
  print('=' * 50);
  
  // 创建Dio实例，使用我们修复后的配置
  final dio = Dio(
    BaseOptions(
      connectTimeout: const Duration(milliseconds: 10000),
      receiveTimeout: const Duration(milliseconds: 30000),
      sendTimeout: const Duration(milliseconds: 10000),
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        // 让Dio自动处理Accept-Encoding和gzip解压
      },
      responseType: ResponseType.plain,
      receiveDataWhenStatusError: true,
      followRedirects: true,
      maxRedirects: 5,
    ),
  );

  // 添加响应处理拦截器
  dio.interceptors.add(
    InterceptorsWrapper(
      onResponse: (response, handler) {
        try {
          final contentEncoding = response.headers.value('content-encoding');
          final contentType = response.headers.value('content-type');

          print('📡 HTTP响应: ${response.requestOptions.uri}');
          print('  状态: ${response.statusCode}');
          print('  Content-Type: $contentType');
          print('  Content-Encoding: $contentEncoding');
          print('  数据类型: ${response.data.runtimeType}');

          if (response.data is String) {
            final String responseData = response.data as String;
            print('  响应长度: ${responseData.length}');

            // 检查是否是gzip数据
            if (responseData.isNotEmpty &&
                responseData.codeUnitAt(0) == 0x1f &&
                responseData.length > 1 &&
                responseData.codeUnitAt(1) == 0x8b) {
              print('❌ 检测到未解压的gzip数据！');
              
              // 尝试手动解压
              try {
                final bytes = responseData.codeUnits.map((e) => e & 0xFF).toList();
                final decompressed = gzip.decode(bytes);
                final decodedString = utf8.decode(decompressed);
                
                response.data = decodedString;
                print('✅ 手动gzip解压成功，长度: ${decodedString.length}');
                
                // 检查解压后的内容
                if (decodedString.contains('<!DOCTYPE html') || decodedString.contains('<html')) {
                  print('✅ 解压后收到HTML内容');
                  if (decodedString.contains('电影') || decodedString.contains('电视剧')) {
                    print('✅ 解压后中文内容正确显示');
                  }
                }
              } catch (e) {
                print('❌ gzip解压失败: $e');
              }
            } else if (responseData.contains('<!DOCTYPE html') || responseData.contains('<html')) {
              print('✅ 收到HTML内容');
              if (responseData.contains('电影') || responseData.contains('电视剧')) {
                print('✅ 中文内容正确显示');
              }
            } else {
              // 检查是否包含大量控制字符
              final controlCharCount = responseData.codeUnits
                  .where((code) => code < 32 && code != 9 && code != 10 && code != 13)
                  .length;
              final controlCharRatio = controlCharCount / responseData.length;
              
              if (controlCharRatio > 0.1) {
                print('❌ 检测到大量控制字符 (${(controlCharRatio * 100).toStringAsFixed(1)}%)');
                print('这可能是未正确处理的二进制数据');
              } else {
                print('⚠️  收到未知格式的内容');
              }
              
              // 显示前100个字符
              final preview = responseData.length > 100 ? responseData.substring(0, 100) : responseData;
              print('内容预览: ${preview.replaceAll(RegExp(r'[\x00-\x1F]'), '?')}');
            }
          }
        } catch (e) {
          print('❌ 响应处理异常: $e');
        }

        handler.next(response);
      },
    ),
  );

  try {
    print('\n📤 发送请求到: https://wogg.xxooo.cf');
    final response = await dio.get('https://wogg.xxooo.cf');
    
    print('\n🎉 请求完成！');
    if (response.data is String) {
      final data = response.data as String;
      if (data.contains('电影') || data.contains('电视剧')) {
        print('🎊 成功！中文内容正确显示');
      } else if (data.contains('<!DOCTYPE html')) {
        print('⚠️  收到HTML但未检测到中文内容');
      } else {
        print('❌ 数据仍有问题');
      }
    }
    
  } catch (e) {
    print('❌ 请求失败: $e');
  }
  
  dio.close();
  
  print('\n💡 总结:');
  print('  如果看到"✅ 中文内容正确显示"，说明修复成功');
  print('  如果看到"❌ 检测到未解压的gzip数据"，说明Dio配置仍有问题');
  print('  如果看到"❌ 检测到大量控制字符"，说明存在编码问题');
}
