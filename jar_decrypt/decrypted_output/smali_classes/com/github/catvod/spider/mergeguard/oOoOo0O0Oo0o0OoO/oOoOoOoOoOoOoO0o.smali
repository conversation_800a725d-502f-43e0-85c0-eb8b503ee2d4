.class public Lcom/github/catvod/spider/mergeguard/oOoOo0O0Oo0o0OoO/oOoOoOoOoOoOoO0o;
.super Ljava/lang/Error;
.source "SourceFile"


# instance fields
.field public final oOoOoOoOoOoOoO0o:Ljava/lang/Exception;


# direct methods
.method public constructor <init>()V
    .registers 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Error;-><init>()V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Exception;)V
    .registers 2

    .line 2
    invoke-direct {p0}, Ljava/lang/Error;-><init>()V

    .line 3
    iput-object p1, p0, Lcom/github/catvod/spider/mergeguard/oOoOo0O0Oo0o0OoO/oOoOoOoOoOoOoO0o;->oOoOoOoOoOoOoO0o:Ljava/lang/Exception;

    return-void
.end method

.method public constructor <init>(Ljava/lang/Exception;Ljava/lang/String;)V
    .registers 3

    .line 4
    invoke-direct {p0, p2}, Ljava/lang/Error;-><init>(Ljava/lang/String;)V

    .line 5
    iput-object p1, p0, Lcom/github/catvod/spider/mergeguard/oOoOo0O0Oo0o0OoO/oOoOoOoOoOoOoO0o;->oOoOoOoOoOoOoO0o:Ljava/lang/Exception;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .registers 2

    .line 8
    invoke-direct {p0, p1}, Ljava/lang/Error;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/Exception;)V
    .registers 3

    .line 6
    invoke-direct {p0, p1}, Ljava/lang/Error;-><init>(Ljava/lang/String;)V

    .line 7
    iput-object p2, p0, Lcom/github/catvod/spider/mergeguard/oOoOo0O0Oo0o0OoO/oOoOoOoOoOoOoO0o;->oOoOoOoOoOoOoO0o:Ljava/lang/Exception;

    return-void
.end method


# virtual methods
.method public final getMessage()Ljava/lang/String;
    .registers 3

    .line 1
    invoke-super {p0}, Ljava/lang/Error;->getMessage()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_7

    .line 6
    .line 7
    return-object v0

    .line 8
    :cond_7
    iget-object v1, p0, Lcom/github/catvod/spider/mergeguard/oOoOo0O0Oo0o0OoO/oOoOoOoOoOoOoO0o;->oOoOoOoOoOoOoO0o:Ljava/lang/Exception;

    .line 9
    .line 10
    if-eqz v1, :cond_19

    .line 11
    .line 12
    invoke-virtual {v1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    if-nez v0, :cond_19

    .line 17
    .line 18
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {v0}, Ljava/lang/Class;->toString()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    :cond_19
    return-object v0
    .line 27
    .line 28
    .line 29
    .line 30
    .line 31
    .line 32
    .line 33
    .line 34
    .line 35
    .line 36
    .line 37
    .line 38
    .line 39
    .line 40
    .line 41
    .line 42
    .line 43
    .line 44
    .line 45
    .line 46
    .line 47
    .line 48
    .line 49
    .line 50
    .line 51
    .line 52
    .line 53
    .line 54
    .line 55
    .line 56
    .line 57
    .line 58
    .line 59
    .line 60
    .line 61
    .line 62
    .line 63
    .line 64
    .line 65
    .line 66
.end method
