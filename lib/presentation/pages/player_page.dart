import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter/foundation.dart';
import '../../services/quark/quark_channel.dart';
import '../widgets/../widgets/vlc_macos_view.dart';

/// 增强播放器页面（配合智能缓冲策略）
/// - 传入播放 url 和可选 headers
/// - 监控播放器状态变化和错误
/// - 配合后端智能缓冲显示预缓冲状态
class PlayerPage extends StatefulWidget {
  final String url; // 播放地址
  final Map<String, String>? headers; // 可选 HTTP 请求头
  final String? title; // 可选标题（剧名/集名）
  final List<Map<String, dynamic>>? episodes; // 选集列表（来源于详情页 seriesMap[firstFlag]）
  final int? initialEpisodeIndex; // 初始选中集索引

  const PlayerPage({super.key, required this.url, this.headers, this.title, this.episodes, this.initialEpisodeIndex});

  @override
  State<PlayerPage> createState() => _PlayerPageState();
}

class _PlayerPageState extends State<PlayerPage> {
  VideoPlayerController? _v;
  ChewieController? _c;
  VlcMacosController? _vlc;
  bool _showControls = true;
  bool _isPlaying = false; // 统一的播放状态（用于UI）
  bool _err = false;
  String _errorMsg = '';
  String _status = '准备播放...';
  bool _isPreBuffering = true; // 预缓冲状态
  double _bufferProgress = 0.0; // 缓冲进度
  List<Map<String, dynamic>> _episodes = const [];
  int _currentEpisodeIndex = 0;
  int _currentPositionMs = 0; // macOS VLC 用
  int _durationMs = -1; // macOS VLC 用
  double _playbackRate = 1.0;
  bool _isReversed = false; // 选集排序：false=正序，true=倒序

  String get _displayTitle {
    final base = widget.title;
    if (base != null && base.isNotEmpty) return base;
    if (_episodes.isNotEmpty && _currentEpisodeIndex >= 0 && _currentEpisodeIndex < _episodes.length) {
      final epName = (_episodes[_currentEpisodeIndex]['name'] as String?) ?? '';
      if (epName.isNotEmpty) return epName;
    }
    return '播放器';
  }

  String _formatTimeMs(int milliseconds) {
    if (milliseconds <= 0) return '00:00';
    final totalSeconds = milliseconds ~/ 1000;
    final minutes = (totalSeconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (totalSeconds % 60).toString().padLeft(2, '0');
    final hours = (totalSeconds ~/ 3600);
    if (hours > 0) {
      final h = hours.toString().padLeft(2, '0');
      final m = ((totalSeconds % 3600) ~/ 60).toString().padLeft(2, '0');
      final s = (totalSeconds % 60).toString().padLeft(2, '0');
      return '$h:$m:$s';
    }
    return '$minutes:$seconds';
  }

  String _formatPosition(Duration duration) {
    final totalSeconds = duration.inSeconds;
    final minutes = (totalSeconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (totalSeconds % 60).toString().padLeft(2, '0');
    final hours = (totalSeconds ~/ 3600);
    if (hours > 0) {
      final h = hours.toString().padLeft(2, '0');
      final m = ((totalSeconds % 3600) ~/ 60).toString().padLeft(2, '0');
      final s = (totalSeconds % 60).toString().padLeft(2, '0');
      return '$h:$m:$s';
    }
    return '$minutes:$seconds';
  }

  @override
  void initState() {
    super.initState();
    if (kDebugMode) {
      debugPrint('[PlayerPage] init (smart buffer)');
    }
    _init();
  }

  Future<void> _init() async {
    try {
      // 初始化选集
      _episodes = (widget.episodes ?? const []).cast<Map<String, dynamic>>();
      _currentEpisodeIndex = widget.initialEpisodeIndex ?? 0;
      
      if (kDebugMode) {
        debugPrint('[PlayerPage] 初始化选集信息:');
        debugPrint('[PlayerPage] 标题: ${widget.title}');
        debugPrint('[PlayerPage] 选集数量: ${_episodes.length}');
        debugPrint('[PlayerPage] 当前选集索引: $_currentEpisodeIndex');
        if (_episodes.isNotEmpty) {
          debugPrint('[PlayerPage] 选集列表: ${_episodes.map((e) => e['name']).toList()}');
        }
      }

      // macOS 走 VLC 原生视图
      if (!kIsWeb && defaultTargetPlatform == TargetPlatform.macOS) {
        setState(() {
          _status = '创建 VLC 播放器...';
          _isPreBuffering = true;
          _bufferProgress = 0.3;
        });
        // 初始化将在视图创建后由 onViewCreated 回调完成
        return;
      }

      setState(() {
        _status = '连接服务器...';
        _isPreBuffering = true;
        _bufferProgress = 0.1;
      });
      if (kDebugMode) debugPrint('[PlayerPage] create VideoPlayerController');
      
      // 初始化 VideoPlayerController，传入可选 headers和优化配置
      _v = VideoPlayerController.networkUrl(
        Uri.parse(widget.url),
        httpHeaders: widget.headers ?? const <String, String>{},
        videoPlayerOptions: VideoPlayerOptions(
          mixWithOthers: true,
          allowBackgroundPlayback: false,
        ),
      );
      
      // 添加状态监听器
      _v!.addListener(_onVideoStateChanged);
      
      setState(() {
        _status = '智能缓冲中...';
        _bufferProgress = 0.3;
      });
      
      // 初始化播放器
      await _v!.initialize();
      
      if (kDebugMode) debugPrint('[PlayerPage] controller initialized');
      
      setState(() {
        _status = '创建播放控制器...';
        _bufferProgress = 0.8;
      });
      
      // 包装 ChewieController 以提供基础 UI 控件
      _c = ChewieController(
        videoPlayerController: _v!,
        autoPlay: true,
        looping: false,
        showControlsOnInitialize: false,
        showControls: false,
        errorBuilder: (context, errorMessage) {
          if (kDebugMode) debugPrint('[PlayerPage] Chewie error: $errorMessage');
          return Center(
            child: Text('播放错误: $errorMessage', style: TextStyle(color: Colors.red)),
          );
        },
      );
      
      setState(() {
        _status = '播放器就绪';
        _bufferProgress = 1.0;
        _isPreBuffering = false;
      });
      if (kDebugMode) debugPrint('[PlayerPage] ready');
      
      if (mounted) setState(() {});
    } catch (e) {
      _err = true;
      _errorMsg = e.toString();
      setState(() => _status = '初始化失败: $e');
      if (mounted) setState(() {});
    }
  }
  
  void _onVideoStateChanged() {
    if (_v == null) return;
    
    final value = _v!.value;
    
    // 更新缓冲状态
    if (value.isBuffering && !_isPreBuffering) {
      setState(() { _status = '正在缓冲...'; });
    } else if (value.isPlaying && !_isPreBuffering) {
      setState(() { _status = '播放中'; _isPlaying = true; });
    }
    
    if (value.hasError) {
      setState(() {
        _err = true;
        _errorMsg = value.errorDescription ?? '未知播放错误';
        _status = '播放错误: $_errorMsg';
      });
    }
    
    if (mounted) setState(() {});
  }

  Future<void> _loadNewSource(String url, Map<String, String>? headers) async {
    setState(() {
      _status = '切换剧集中...';
      _isPreBuffering = true;
      _bufferProgress = 0.2;
      _isPlaying = false;
    });
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.macOS) {
      try {
        await _vlc?.initialize(url, headers: headers);
        await _vlc?.setRate(_playbackRate);
        await _vlc?.play();
        setState(() {
          _isPlaying = true;
          _isPreBuffering = false;
          _status = '播放中';
        });
      } catch (e) {
        setState(() { _err = true; _errorMsg = '切换失败: $e'; _status = _errorMsg; });
      }
      return;
    }
    try {
      _v?.removeListener(_onVideoStateChanged);
      _c?.pause();
      _c?.dispose();
      if (_v != null) {
        await _v!.dispose();
      }

      _v = VideoPlayerController.networkUrl(
        Uri.parse(url),
        httpHeaders: headers ?? const <String, String>{},
        videoPlayerOptions: VideoPlayerOptions(
          mixWithOthers: true,
          allowBackgroundPlayback: false,
        ),
      );
      _v!.addListener(_onVideoStateChanged);
      await _v!.initialize();
      await _v!.setPlaybackSpeed(_playbackRate);
      _c = ChewieController(
        videoPlayerController: _v!,
        autoPlay: true,
        looping: false,
        showControlsOnInitialize: false,
        showControls: false,
      );
      setState(() {
        _isPreBuffering = false;
        _isPlaying = true;
        _status = '播放中';
      });
    } catch (e) {
      setState(() { _err = true; _errorMsg = '切换失败: $e'; _status = _errorMsg; });
    }
  }

  Future<void> _selectEpisode(int index) async {
    if (_episodes.isEmpty) return;
    
    // 边界处理：防止越界
    int targetIndex = index;
    if (index < 0) {
      targetIndex = 0; // 上一集：如果小于0，保持在第一集
    } else if (index >= _episodes.length) {
      targetIndex = _episodes.length - 1; // 下一集：如果超出，保持在最后一集
    }
    
    final ep = _episodes[targetIndex];
    final id = (ep['url'] as String?) ?? '';
    if (id.isEmpty) return;
    
    try {
      setState(() {
        _status = '切换选集...';
        _isPreBuffering = true;
        _bufferProgress = 0.3;
      });
      
      final res = await QuarkChannel.playerContent(flag: 'quark原画', id: id);
      final playUrl = (res['url'] as String?) ?? '';
      Map<String, String>? headers;
      final hdr = res['header'];
      if (hdr is Map) headers = hdr.cast<String, String>();
      if (playUrl.isEmpty) {
        setState(() { 
          _err = true; 
          _errorMsg = '无法获取播放地址'; 
          _status = _errorMsg; 
          _isPreBuffering = false;
        });
        return;
      }
      setState(() => _currentEpisodeIndex = targetIndex);
      await _loadNewSource(playUrl, headers);
    } catch (e) {
      setState(() { 
        _err = true; 
        _errorMsg = '切换选集失败: $e'; 
        _status = _errorMsg; 
        _isPreBuffering = false;
      });
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('切换失败：$e')));
    }
  }

  void _seekRelative(int seconds) {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.macOS) {
      final target = (_currentPositionMs + seconds * 1000).clamp(0, _durationMs < 0 ? 1 << 30 : _durationMs);
      _vlc?.seekTo(target);
      return;
    }
    if (_v == null) return;
    final pos = _v!.value.position;
    final dur = _v!.value.duration;
    final target = pos + Duration(seconds: seconds);
    Duration clipped = target;
    if (target < Duration.zero) clipped = Duration.zero;
    if (dur != Duration.zero && target > dur) clipped = dur;
    _v!.seekTo(clipped);
  }

  void _changeRate(double rate) {
    setState(() { _playbackRate = rate; });
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.macOS) {
      _vlc?.setRate(rate);
      return;
    }
    _v?.setPlaybackSpeed(rate);
  }

  void _showEpisodePicker() {
    if (_episodes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('无选集')));
      return;
    }

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            // 根据排序状态获取选集列表
            List<Map<String, dynamic>> displayEpisodes = List.from(_episodes);
            List<int> originalIndices = List.generate(_episodes.length, (index) => index);
            
            if (_isReversed) {
              displayEpisodes = displayEpisodes.reversed.toList();
              originalIndices = originalIndices.reversed.toList();
            }

            return Align(
              alignment: Alignment.centerRight,
              child: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(1, 0),
                  end: Offset.zero,
                ).animate(CurvedAnimation(
                  parent: animation,
                  curve: Curves.easeOutCubic,
                )),
                child: Material(
                  color: Colors.transparent,
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.35,
                    height: MediaQuery.of(context).size.height,
                    decoration: BoxDecoration(
                      color: const Color(0xFF1A1A1A),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 10,
                          offset: const Offset(-2, 0),
                        ),
                      ],
                    ),
                    child: SafeArea(
                      child: Column(
                        children: [
                          // 头部区域
                          Container(
                            padding: const EdgeInsets.fromLTRB(16, 12, 8, 12),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.2),
                              border: Border(
                                bottom: BorderSide(color: Colors.white.withOpacity(0.1)),
                              ),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.playlist_play,
                                  color: Colors.white,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    '选集',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                // 排序按钮
                                Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(20),
                                    onTap: () {
                                      setDialogState(() {
                                        _isReversed = !_isReversed;
                                      });
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.all(6),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            _isReversed ? Icons.arrow_upward : Icons.arrow_downward,
                                            color: Colors.white70,
                                            size: 16,
                                          ),
                                          const SizedBox(width: 2),
                                          Text(
                                            _isReversed ? '倒序' : '正序',
                                            style: const TextStyle(
                                              color: Colors.white70,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                // 关闭按钮
                                Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(20),
                                    onTap: () => Navigator.of(context).pop(),
                                    child: Container(
                                      padding: const EdgeInsets.all(6),
                                      child: const Icon(
                                        Icons.close,
                                        color: Colors.white70,
                                        size: 18,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // 集数统计
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            child: Text(
                              '共 ${_episodes.length} 集',
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.6),
                                fontSize: 12,
                              ),
                            ),
                          ),
                          // 选集列表
                          Expanded(
                            child: ListView.builder(
                              padding: const EdgeInsets.symmetric(horizontal: 8),
                              itemCount: displayEpisodes.length,
                              itemBuilder: (context, index) {
                                final episodeIndex = originalIndices[index];
                                final episode = displayEpisodes[index];
                                final isSelected = episodeIndex == _currentEpisodeIndex;
                                final episodeName = (episode['name'] as String?) ?? '第${episodeIndex + 1}集';
                                
                                return Container(
                                  margin: const EdgeInsets.symmetric(vertical: 2),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(8),
                                      onTap: () {
                                        Navigator.of(context).pop();
                                        _selectEpisode(episodeIndex);
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(8),
                                          color: isSelected 
                                              ? const Color(0xFF2196F3).withOpacity(0.15)
                                              : Colors.transparent,
                                          border: isSelected 
                                              ? Border.all(color: const Color(0xFF2196F3), width: 1)
                                              : null,
                                        ),
                                        child: Row(
                                          children: [
                                            // 播放状态图标
                                            Container(
                                              width: 20,
                                              height: 20,
                                              alignment: Alignment.center,
                                              child: isSelected
                                                  ? const Icon(
                                                      Icons.play_arrow,
                                                      color: Color(0xFF2196F3),
                                                      size: 16,
                                                    )
                                                  : Text(
                                                      '${episodeIndex + 1}',
                                                      style: TextStyle(
                                                        color: Colors.white.withOpacity(0.4),
                                                        fontSize: 11,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                            ),
                                            const SizedBox(width: 8),
                                            // 选集名称
                                            Expanded(
                                              child: Text(
                                                episodeName,
                                                style: TextStyle(
                                                  color: isSelected 
                                                      ? const Color(0xFF2196F3)
                                                      : Colors.white.withOpacity(0.85),
                                                  fontSize: 13,
                                                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                                                ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  @override
  void dispose() {
    _v?.removeListener(_onVideoStateChanged);
    _c?.dispose();
    _v?.dispose();
    _vlc?.dispose();
    super.dispose();
  }

  void _togglePlayPause() {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.macOS) {
      if (_isPlaying) {
        _vlc?.pause();
      } else {
        _vlc?.play();
      }
      setState(() { _isPlaying = !_isPlaying; });
      return;
    }
    if (_v == null) return;
    if (_v!.value.isPlaying) {
      _v!.pause();
      setState(() { _isPlaying = false; });
    } else {
      _v!.play();
      setState(() { _isPlaying = true; });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_err) {
      return Scaffold(
        appBar: AppBar(title: Text('播放失败')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red),
              SizedBox(height: 16),
              Text('播放失败', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Padding(
                padding: EdgeInsets.all(16),
                child: Text(_errorMsg, textAlign: TextAlign.center),
              ),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  print('[PlayerPage] 用户点击重试');
                  setState(() {
                    _err = false;
                    _errorMsg = '';
                    _status = '重新准备播放...';
                    _isPreBuffering = true;
                    _bufferProgress = 0.0;
                  });
                  _init();
                },
                child: Text('重试'),
              ),
            ],
          ),
        ),
      );
    }
    
    // macOS: 显示 VLC 视图和加载态 + 自定义控制层
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.macOS) {
      return Scaffold(
        backgroundColor: Colors.black,
        body: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => setState(() => _showControls = !_showControls),
          child: Stack(
            children: [
              Positioned.fill(
                child: VlcMacosView(
                  url: widget.url,
                  headers: widget.headers,
                  onViewCreated: (c) async {
                    _vlc = c;
                    c.events.listen((e) {
                      final event = e['event'] as String?;
                      if (event == 'onInitialized') {
                        setState(() {
                          _status = '播放器就绪';
                          _isPreBuffering = false;
                          _bufferProgress = 1.0;
                        });
                      } else if (event == 'onPosition') {
                        final positionMs = (e['positionMs'] as int?) ?? 0;
                        final durationMs = (e['durationMs'] as int?) ?? -1;
                        _currentPositionMs = positionMs;
                        _durationMs = durationMs;
                        if (!_isPlaying) setState(() => _isPlaying = true);
                        if (!_isPreBuffering) setState(() => _status = '播放中');
                      } else if (event == 'onState') {
                        final state = (e['state'] as int?) ?? -1;
                        if (state == 3) { // playing
                          setState(() { _isPlaying = true; _status = '播放中'; });
                        } else if (state == 4) { // paused
                          setState(() { _isPlaying = false; _status = '已暂停'; });
                        }
                      }
                    });
                    try {
                      await c.initialize(widget.url, headers: widget.headers);
                      await c.play();
                      setState(() { _isPlaying = true; });
                    } catch (e) {
                      setState(() {
                        _err = true;
                        _errorMsg = 'VLC 初始化失败: $e';
                        _status = _errorMsg;
                      });
                    }
                  },
                ),
              ),
              if (_isPreBuffering)
                Positioned(
                  left: 0, right: 0, bottom: 0,
                  child: SafeArea(
                    child: Padding(
                      padding: EdgeInsets.all(12),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(_status, style: TextStyle(color: Colors.white)),
                          SizedBox(height: 8),
                          LinearProgressIndicator(value: _bufferProgress),
                        ],
                      ),
                    ),
                  ),
                ),
              if (_showControls)
                Positioned(
                  left: 0, right: 0, bottom: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.4),
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                    ),
                    child: SafeArea(
                      bottom: true,
                      child: Container(
                        padding: const EdgeInsets.fromLTRB(12, 8, 12, 6),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                          Row(
                            children: [
                              IconButton(
                                onPressed: _togglePlayPause,
                                icon: Icon(_isPlaying ? Icons.pause_circle_filled : Icons.play_circle_fill, color: Colors.white, size: 32),
                              ),
                              IconButton(
                                onPressed: () => _seekRelative(-10),
                                icon: const Icon(Icons.replay_10, color: Colors.white),
                              ),
                              IconButton(
                                onPressed: () => _seekRelative(10),
                                icon: const Icon(Icons.forward_10, color: Colors.white),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _displayTitle,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(color: Colors.white, fontSize: 14),
                                ),
                              ),
                              const SizedBox(width: 8),
                              IconButton(
                                onPressed: (_episodes.isNotEmpty && _currentEpisodeIndex > 0)
                                    ? () => _selectEpisode(_currentEpisodeIndex - 1)
                                    : null,
                                icon: Icon(
                                  Icons.skip_previous,
                                  color: (_episodes.isNotEmpty && _currentEpisodeIndex > 0) ? Colors.white : Colors.white24,
                                ),
                              ),
                              IconButton(
                                onPressed: (_episodes.isNotEmpty && _currentEpisodeIndex < _episodes.length - 1)
                                    ? () => _selectEpisode(_currentEpisodeIndex + 1)
                                    : null,
                                icon: Icon(
                                  Icons.skip_next,
                                  color: (_episodes.isNotEmpty && _currentEpisodeIndex < _episodes.length - 1) ? Colors.white : Colors.white24,
                                ),
                              ),
                              IconButton(
                                tooltip: '选集',
                                onPressed: _episodes.isNotEmpty ? _showEpisodePicker : null,
                                icon: Icon(Icons.view_list, color: _episodes.isNotEmpty ? Colors.white : Colors.white24),
                              ),
                              PopupMenuButton<double>(
                                tooltip: '倍速',
                                icon: const Icon(Icons.speed, color: Colors.white),
                                onSelected: _changeRate,
                                itemBuilder: (context) => [
                                  const PopupMenuItem(value: 0.5, child: Text('0.5x')),
                                  const PopupMenuItem(value: 0.75, child: Text('0.75x')),
                                  const PopupMenuItem(value: 1.0, child: Text('1.0x 正常')),
                                  const PopupMenuItem(value: 1.5, child: Text('1.5x')),
                                  const PopupMenuItem(value: 2.0, child: Text('2.0x')),
                                  const PopupMenuItem(value: 3.0, child: Text('3.0x')),
                                ],
                              ),
                            ],
                          ),
                          if (_durationMs > 0)
                            Row(
                              children: [
                                Text(_formatTimeMs(_currentPositionMs), style: const TextStyle(color: Colors.white70, fontSize: 12)),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Slider(
                                    value: _currentPositionMs.clamp(0, _durationMs).toDouble(),
                                    min: 0,
                                    max: _durationMs.toDouble(),
                                    onChanged: (v) { setState(() => _currentPositionMs = v.toInt()); },
                                    onChangeEnd: (v) { _vlc?.seekTo(v.toInt()); },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(_formatTimeMs(_durationMs), style: const TextStyle(color: Colors.white70, fontSize: 12)),
                              ],
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              if (_showControls)
                Positioned(
                  left: 0, right: 0, top: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withOpacity(0.7),
                          Colors.black.withOpacity(0.4),
                          Colors.transparent,
                        ],
                      ),
                    ),
                    child: SafeArea(
                      bottom: false,
                      child: Container(
                        padding: const EdgeInsets.fromLTRB(8, 4, 12, 8),
                        child: Row(
                          children: [
                            Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(24),
                                onTap: () => Navigator.of(context).pop(),
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  child: const Icon(
                                    Icons.arrow_back_ios,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                _displayTitle,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      );
    }

    // 预缓冲加载页面（非 macOS）
    if (_isPreBuffering || _v == null || !_v!.value.isInitialized || _c == null) {
      return Scaffold(
        appBar: AppBar(title: Text('智能缓冲中')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 智能缓冲图标
              Container(
                width: 120,
                height: 120,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // 外圈进度指示器
                    SizedBox(
                      width: 100,
                      height: 100,
                      child: CircularProgressIndicator(
                        value: _bufferProgress,
                        strokeWidth: 8,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                      ),
                    ),
                    // 内部播放图标
                    Icon(
                      Icons.play_circle_filled,
                      size: 60,
                      color: Colors.blue,
                    ),
                  ],
                ),
              ),
              SizedBox(height: 24),
              Text(
                _status,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              SizedBox(height: 8),
              Text(
                '${(_bufferProgress * 100).toInt()}%',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  '正在智能预缓冲视频内容，确保流畅播放体验...',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => setState(() => _showControls = !_showControls),
        child: Stack(
          children: [
            Center(
              child: AspectRatio(
                aspectRatio: _v!.value.aspectRatio,
                child: Chewie(controller: _c!),
              ),
            ),
            if (_showControls)
              Positioned(
                left: 0, right: 0, bottom: 0,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.4),
                        Colors.black.withOpacity(0.7),
                      ],
                    ),
                  ),
                  child: SafeArea(
                    bottom: true,
                    child: Container(
                      padding: const EdgeInsets.fromLTRB(12, 8, 12, 6),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                        Row(
                          children: [
                            IconButton(
                              onPressed: _togglePlayPause,
                              icon: Icon((_v?.value.isPlaying ?? false) ? Icons.pause_circle_filled : Icons.play_circle_fill, color: Colors.white, size: 32),
                            ),
                            IconButton(
                              onPressed: () => _seekRelative(-10),
                              icon: const Icon(Icons.replay_10, color: Colors.white),
                            ),
                            IconButton(
                              onPressed: () => _seekRelative(10),
                              icon: const Icon(Icons.forward_10, color: Colors.white),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _displayTitle,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(color: Colors.white, fontSize: 14),
                              ),
                            ),
                            const SizedBox(width: 8),
                            IconButton(
                              onPressed: (_episodes.isNotEmpty && _currentEpisodeIndex > 0)
                                  ? () => _selectEpisode(_currentEpisodeIndex - 1)
                                  : null,
                              icon: Icon(
                                Icons.skip_previous,
                                color: (_episodes.isNotEmpty && _currentEpisodeIndex > 0) ? Colors.white : Colors.white24,
                              ),
                            ),
                            IconButton(
                              onPressed: (_episodes.isNotEmpty && _currentEpisodeIndex < _episodes.length - 1)
                                  ? () => _selectEpisode(_currentEpisodeIndex + 1)
                                  : null,
                              icon: Icon(
                                Icons.skip_next,
                                color: (_episodes.isNotEmpty && _currentEpisodeIndex < _episodes.length - 1) ? Colors.white : Colors.white24,
                              ),
                            ),
                            IconButton(
                              tooltip: '选集',
                              onPressed: _episodes.isNotEmpty ? _showEpisodePicker : null,
                              icon: Icon(Icons.view_list, color: _episodes.isNotEmpty ? Colors.white : Colors.white24),
                            ),
                            PopupMenuButton<double>(
                              tooltip: '倍速',
                              icon: const Icon(Icons.speed, color: Colors.white),
                              onSelected: _changeRate,
                              itemBuilder: (context) => [
                                const PopupMenuItem(value: 0.5, child: Text('0.5x')),
                                const PopupMenuItem(value: 0.75, child: Text('0.75x')),
                                const PopupMenuItem(value: 1.0, child: Text('1.0x 正常')),
                                const PopupMenuItem(value: 1.5, child: Text('1.5x')),
                                const PopupMenuItem(value: 2.0, child: Text('2.0x')),
                                const PopupMenuItem(value: 3.0, child: Text('3.0x')),
                              ],
                            ),
                          ],
                        ),
                        if (_v != null && _v!.value.isInitialized && _v!.value.duration.inMilliseconds > 0)
                          Row(
                            children: [
                              Text(_formatPosition(_v!.value.position), style: const TextStyle(color: Colors.white70, fontSize: 12)),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Slider(
                                  value: _v!.value.position.inMilliseconds.clamp(0, _v!.value.duration.inMilliseconds).toDouble(),
                                  min: 0,
                                  max: _v!.value.duration.inMilliseconds.toDouble(),
                                  onChanged: (v) {
                                    _v?.seekTo(Duration(milliseconds: v.toInt()));
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(_formatPosition(_v!.value.duration), style: const TextStyle(color: Colors.white70, fontSize: 12)),
                            ],
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            if (_showControls)
              Positioned(
                left: 0, right: 0, top: 0,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withOpacity(0.7),
                        Colors.black.withOpacity(0.4),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: SafeArea(
                    bottom: false,
                    child: Container(
                      padding: const EdgeInsets.fromLTRB(8, 4, 12, 8),
                      child: Row(
                        children: [
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(24),
                              onTap: () => Navigator.of(context).pop(),
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                child: const Icon(
                                  Icons.arrow_back_ios,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              _displayTitle,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
